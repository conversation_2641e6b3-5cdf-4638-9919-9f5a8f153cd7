# ADAPT-VQE Memory Management Fix

## Problem Identified

The log files (pfXXXX.txt) showed a serious memory leak during the ADAPT-VQE ansatz pool phase:

- **Observable memory grew from 0 MB to over 12.9 GB** during operator pool processing
- **Statevector memory was not being properly freed** between operator calculations
- **Memory accumulation continued throughout the entire pool construction**

### Root Causes

1. **Insufficient Memory Cleanup**: While `deallocate_simulation_state()` was called after each operator, the implementation was incomplete
2. **Observable Memory Accumulation**: Temporary data structures (PauliOperator maps, commutator coefficients) were not being properly cleared
3. **GPU Memory Synchronization Issues**: CUDA memory operations lacked proper synchronization
4. **Missing Periodic Cleanup**: No mechanism to force memory cleanup during long operator pool processing

## Solution Implemented

### 1. Enhanced Memory Cleanup in ADAPT-VQE (`vqe/include/vqe_adapt.hpp`)

**Added periodic memory cleanup every 10 operators:**
```cpp
// MEMORY OPTIMIZATION: Force immediate memory cleanup for large operator pools
if (i > 0 && i % 10 == 0) {
    // Force garbage collection of temporary data structures
    std::vector<PauliOperator>().swap(comm_ops);
    // Use the new force cleanup method
    state->force_memory_cleanup();
    
    if (state->get_process_rank() == 0) {
        std::cout << "  [Memory cleanup performed at operator " << i << "]" << std::endl;
    }
}
```

**Enhanced cleanup after each operator:**
```cpp
// MEMORY OPTIMIZATION: Clear temporary data structures to prevent memory accumulation
comm_ops.clear();
comm_ops.shrink_to_fit();
cliques.clear();
```

**Added final comprehensive cleanup:**
```cpp
// MEMORY OPTIMIZATION: Final comprehensive cleanup after all operators processed
state->force_memory_cleanup();
```

### 2. Improved CUDA Memory Management (`vqe/include/svsim_vqe/sv_cuda_vqe.cuh`)

**Enhanced deallocate_simulation_state():**
```cpp
virtual void deallocate_simulation_state() override {
    // Force GPU memory synchronization before deallocation
    cudaDeviceSynchronize();
    
    // Free GPU memory
    if (sv_real) SAFE_FREE_GPU(sv_real);
    if (sv_imag) SAFE_FREE_GPU(sv_imag);
    if (m_real) SAFE_FREE_GPU(m_real);
    if (m_imag) SAFE_FREE_GPU(m_imag);
    
    // Free CPU memory  
    if (sv_real_cpu) SAFE_FREE_HOST_CUDA(sv_real_cpu);
    if (sv_imag_cpu) SAFE_FREE_HOST_CUDA(sv_imag_cpu);
    
    // Reset pointers to null
    sv_real = sv_imag = m_real = m_imag = nullptr;
    sv_real_cpu = sv_imag_cpu = nullptr;
    
    // Force GPU memory cleanup
    cudaDeviceSynchronize();
}
```

**Enhanced reallocate_simulation_state():**
```cpp
virtual void reallocate_simulation_state() override {
    // Only reallocate if memory was actually freed
    if (sv_real == nullptr) {
        // ... allocation code ...
        
        // Ensure all operations complete
        cudaDeviceSynchronize();
    }
}
```

### 3. Added Force Memory Cleanup Method (`vqe/include/vqe_state.hpp`)

```cpp
// MEMORY OPTIMIZATION: Add method to force memory cleanup during operator pool processing
virtual void force_memory_cleanup() {
    // Force garbage collection and memory cleanup
    deallocate_simulation_state();
    reallocate_simulation_state();
};
```

### 4. Fixed CUDA MPI Backend (`vqe/include/svsim_vqe/sv_cuda_mpi_vqe.cuh`)

Replaced dummy implementations with proper NVSHMEM memory management.

## Expected Results

With these fixes, you should see:

1. **Periodic cleanup messages**: `[Memory cleanup performed at operator X]`
2. **Stable GPU memory usage** instead of continuous growth
3. **Deallocation confirmation messages**: `FLAG: Deallocate _simulation_state!`
4. **Significantly reduced peak memory usage** during ansatz pool construction

## Testing

Use the provided `test_memory_fix.sh` script to verify the improvements:

```bash
./test_memory_fix.sh
```

This will build the project and run a limited ADAPT-VQE test to demonstrate the memory management improvements.

## Files Modified

1. `vqe/include/vqe_adapt.hpp` - Enhanced memory cleanup in ADAPT-VQE
2. `vqe/include/vqe_state.hpp` - Added force_memory_cleanup method
3. `vqe/include/svsim_vqe/sv_cuda_vqe.cuh` - Improved CUDA memory management
4. `vqe/include/svsim_vqe/sv_cuda_mpi_vqe.cuh` - Fixed MPI memory management

The fix addresses the core memory leak issue while maintaining the correctness of the ADAPT-VQE algorithm.
