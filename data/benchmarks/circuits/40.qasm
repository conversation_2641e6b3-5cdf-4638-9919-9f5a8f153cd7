OPENQASM 2.0;
include "qelib1.inc";
gate r(param0,param1) q0 { u3(2.8811527752032737,0,0) q0; }
gate r_139920839408208(param0,param1) q0 { u3(-2.8811527752032737,0,0) q0; }
gate r_139920838755216(param0,param1) q0 { u3(2.787110989974855,0,0) q0; }
gate r_139920838758672(param0,param1) q0 { u3(-2.787110989974855,0,0) q0; }
gate r_139920830926032(param0,param1) q0 { u3(0.1887592802817087,0,0) q0; }
gate r_139920831582864(param0,param1) q0 { u3(-0.1887592802817087,0,0) q0; }
gate r_139920833955856(param0,param1) q0 { u3(0.3545035235854771,0,0) q0; }
gate r_139920833961232(param0,param1) q0 { u3(-1.9020843457228387,0,0) q0; }
gate r_139920833961552(param0,param1) q0 { u3(-1.9020843457228387,0,0) q0; }
gate r_139920833964048(param0,param1) q0 { u3(-0.3545035235854771,0,0) q0; }
gate r_139920833602768(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920833598160(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920833601360(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920833608976(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920833504208(param0,param1) q0 { u3(-0.6001541715126423,0,0) q0; }
gate r_139920833498256(param0,param1) q0 { u3(-0.6001541715126423,0,0) q0; }
gate r_139920833440144(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920833285584(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920833290064(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920833260432(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920833260880(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920833264720(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920833254160(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920833261328(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
qreg q[10];
u(pi/2,5.060383423666378,5.312614362270054) q[0];
u1(pi/4) q[0];
u1(-pi/2) q[3];
u2(0,pi) q[3];
u1(-pi/2) q[3];
u(0,0,pi/4) q[3];
u1(-pi/2) q[4];
u2(0,pi) q[4];
u1(-pi/2) q[4];
u(0,0,-pi/4) q[5];
cx q[5],q[1];
u(0,0,pi/4) q[1];
cx q[5],q[1];
u(0,0,-pi/4) q[1];
u(3.221479130966311,2.9040528925545304,3.0368135089877932) q[1];
u1(-pi/2) q[1];
cx q[3],q[5];
u(0,0,-pi/4) q[5];
cx q[3],q[5];
u3(0,0,0.12811461730375817) q[3];
u(0,0,pi/4) q[5];
u3(0,0,2.6078489716735556) q[5];
cx q[5],q[3];
u(-0.028855587967437316,0,-2.6078489716735556) q[3];
cx q[5],q[3];
u(0.028855587967437316,2.4797343543697976,0) q[3];
u(0,0,4.499410483436785) q[3];
u(0,0,2.291460926273693) q[3];
u1(pi/2) q[5];
u2(0,pi) q[5];
u1(pi/2) q[5];
u(0,0,1.8309923226724838) q[5];
u(0,0,pi/4) q[6];
cx q[6],q[2];
u(0,0,-pi/4) q[2];
cx q[6],q[2];
u(0,0,pi/4) q[2];
u2(0,pi) q[2];
cx q[0],q[2];
u1(-pi/4) q[2];
cx q[0],q[2];
u3(0,0,pi) q[0];
u3(pi/2,0,pi) q[0];
u3(0,0,pi/4) q[0];
u1(pi/4) q[2];
u2(0,pi) q[2];
u3(0,0,pi) q[2];
cx q[2],q[0];
u3(0,0,-pi/4) q[0];
u3(pi/2,0,pi) q[0];
u3(0,0,pi/2) q[6];
cx q[6],q[1];
u1(pi/2) q[1];
cx q[6],q[0];
u3(0,0,pi/4) q[0];
u3(0,0,pi) q[8];
u(0,0,1.83257819646272) q[8];
cx q[8],q[4];
u(0,0,-1.83257819646272) q[4];
cx q[8],q[4];
u(0,0,1.83257819646272) q[4];
u3(0,0,-pi/2) q[4];
cx q[4],q[0];
u3(0,0,-pi/4) q[0];
cx q[6],q[0];
u3(0,0,pi/4) q[0];
cx q[4],q[0];
u3(0,0,-pi/4) q[0];
u3(pi/2,0,pi) q[0];
u3(0,0,pi/4) q[0];
cx q[2],q[0];
u3(0,0,-pi/4) q[0];
u3(pi/2,0,pi) q[0];
u(0,0,1.198255256377004) q[2];
u(0,0,2.143503699068894) q[2];
cx q[2],q[5];
u(-2.9497910310985995,0,-2.143503699068894) q[5];
cx q[2],q[5];
u(2.9497910310985995,0.3125113763964101,0) q[5];
u1(2.947736518225259) q[5];
u(pi,pi/2,pi/2) q[8];
u1(pi/2) q[8];
u2(0,pi) q[8];
u1(pi/4) q[8];
cx q[1],q[8];
h q[1];
cx q[4],q[1];
rz(-pi/4) q[1];
cx q[4],q[1];
h q[1];
h q[1];
u3(pi,0,pi) q[4];
cx q[4],q[1];
rz(-pi/4) q[1];
cx q[4],q[1];
h q[1];
cx q[2],q[1];
h q[2];
cx q[1],q[2];
tdg q[2];
cx q[4],q[5];
u1(-2.947736518225259) q[5];
cx q[4],q[5];
u(pi,0,pi) q[5];
u3(pi/2,0,pi) q[5];
u3(0,0,-0.9502090263166271) q[5];
u1(-pi/4) q[8];
u2(0,pi) q[8];
u1(-pi/2) q[8];
u2(0,pi) q[8];
cx q[6],q[8];
cx q[6],q[2];
t q[2];
cx q[1],q[2];
t q[1];
tdg q[2];
cx q[6],q[2];
t q[2];
h q[2];
cx q[6],q[1];
tdg q[1];
t q[6];
cx q[6],q[1];
cx q[2],q[1];
u1(-pi/2) q[1];
u2(0,pi) q[1];
u1(-pi/2) q[1];
u3(0,0,pi/2) q[1];
u3(0,0,-pi/4) q[2];
u3(0,0,-pi/2) q[6];
u(pi,pi/2,pi/2) q[6];
u2(0,pi) q[6];
u2(0,pi) q[8];
h q[8];
u2(0,pi) q[9];
cx q[7],q[9];
u3(0,0,-pi/2) q[7];
u(0.6343292680677457,3.6602420176048156,4.168980948933515) q[7];
u(pi/2,4.6231103356393435,4.006026606499447) q[7];
u1(pi/4) q[7];
u2(0,pi) q[9];
reset q[9];
u3(0,0,pi) q[9];
u(0,0,-0.6482601341042888) q[9];
cx q[3],q[9];
u(-2.0832915191249466,0,-2.291460926273693) q[9];
cx q[3],q[9];
cx q[3],q[0];
u1(2.357729153968667) q[0];
cx q[3],q[0];
u3(1.125164211791509,0,0) q[0];
id q[0];
cx q[0],q[1];
u(-1.3743610655710725,0,0) q[1];
cx q[0],q[1];
u1(pi/4) q[0];
cx q[0],q[6];
u(1.3743610655710725,-pi/2,0) q[1];
u1(pi/2) q[1];
u2(0,pi) q[1];
u1(pi/2) q[1];
r(2.8811527752032737,pi/2) q[1];
u3(0,0,pi) q[3];
u3(3.244701870548326,-pi/2,pi/2) q[3];
u3(pi/2,0,pi) q[3];
u3(0,0,pi/4) q[3];
u1(-pi/4) q[6];
cx q[0],q[6];
u(3.6808035822919583,0.6738801911530179,5.877782421202598) q[0];
u3(4.91596218181346,-pi/2,pi/2) q[0];
u(pi/2,3.75194782103832,3.724915327013028) q[0];
u1(pi/4) q[6];
u2(0,pi) q[6];
u(2.4202465117088874,5.462457116244854,0.5143367516110416) q[6];
u(2.0832915191249466,2.939721060377982,0) q[9];
u2(0,pi) q[9];
cx q[7],q[9];
u1(-pi/4) q[9];
cx q[7],q[9];
u(0.5347746745541033,4.519948583134597,3.4070769260186653) q[7];
u(0,0,3.104543546330635) q[7];
cx q[7],q[4];
u(0,0,-3.104543546330635) q[4];
cx q[7],q[4];
u(0,0,3.104543546330635) q[4];
u3(0,0,2.9555993633544566) q[4];
u(1.431108293564075,3.238701739153262,-3.238701739153262) q[4];
cx q[6],q[4];
u3(0,0,3.300646709031302) q[4];
u(0,0,1.9786993285817587) q[6];
cx q[7],q[3];
u3(0,0,-pi/4) q[3];
u3(pi/2,0,pi) q[3];
cx q[2],q[3];
u3(0,0,pi/4) q[3];
u1(pi/4) q[9];
u2(0,pi) q[9];
cx q[9],q[8];
rz(-pi/4) q[8];
cx q[9],q[8];
h q[8];
h q[8];
u3(pi,0,pi) q[9];
cx q[9],q[8];
rz(-pi/4) q[8];
cx q[9],q[8];
h q[8];
u3(0,0,-pi/2) q[8];
cx q[8],q[3];
u3(0,0,-pi/4) q[3];
cx q[2],q[3];
u2(0,pi) q[2];
u3(0,0,pi/4) q[3];
cx q[8],q[3];
u3(0,0,-pi/4) q[3];
u3(pi/2,0,pi) q[3];
u3(0,0,pi/4) q[3];
cx q[7],q[3];
u3(0,0,-pi/4) q[3];
u3(pi/2,0,pi) q[3];
u(0,0,-pi/4) q[3];
cx q[3],q[7];
u(0,0,pi/4) q[7];
cx q[3],q[7];
u(0,0,-pi/4) q[7];
cx q[7],q[1];
r_139920839408208(-2.8811527752032737,pi/2) q[1];
cx q[7],q[1];
r_139920838755216(2.787110989974855,pi/2) q[1];
cx q[3],q[1];
r_139920838758672(-2.787110989974855,pi/2) q[1];
cx q[3],q[1];
u(4.371078010944896,5.015934628864133,5.0563748576496055) q[1];
u3(0,0,pi) q[1];
u3(0,0,1.8827135158336104) q[1];
u1(pi/2) q[1];
u3(0,0,pi/4) q[3];
u1(-pi/2) q[3];
sdg q[3];
h q[3];
sdg q[3];
u1(pi/2) q[3];
reset q[7];
u3(0,0,-pi/2) q[7];
u(0,0,1.2638183669874883) q[7];
cx q[7],q[6];
u(0,0,-1.2638183669874883) q[6];
cx q[7],q[6];
u(0,0,1.2638183669874883) q[6];
u1(pi/4) q[8];
cx q[8],q[2];
u1(-pi/4) q[2];
cx q[8],q[2];
u1(pi/4) q[2];
u2(0,pi) q[2];
u(0.06580194166177941,2.8981454980411288,-2.8981454980411288) q[2];
u3(0,0,-1.683824161361511) q[2];
u(5.356123908879312,3.5640483872875857,-3.5640483872875857) q[8];
u1(-pi/2) q[9];
u2(0,pi) q[9];
u1(-pi/2) q[9];
u(0.9055154569315048,3.532986947464934,3.7964786645062403) q[9];
u3(0,0,2.8628522053949323) q[9];
cx q[9],q[5];
u(-0.10289148719815254,0,-2.8628522053949323) q[5];
cx q[9],q[5];
u(0.10289148719815254,3.8130612317115595,0) q[5];
u3(0,0,2.2632457710029543) q[5];
cx q[5],q[2];
u(-1.0165112743895488,0,-2.2632457710029543) q[2];
cx q[5],q[2];
u(1.0165112743895488,3.947069932364465,0) q[2];
u(pi,0,pi) q[2];
u3(5.398226202691976,-pi/2,pi/2) q[2];
id q[2];
u3(0,0,2.77510564910814) q[5];
cx q[4],q[5];
u(-0.5476563051665946,0,-3.300646709031302) q[5];
cx q[4],q[5];
u2(0,pi) q[4];
u(0.5476563051665946,0.5255410599231624,0) q[5];
u1(-3.9619102421886874) q[5];
u1(pi/2) q[5];
cx q[3],q[5];
r_139920830926032(0.1887592802817087,pi/2) q[3];
r_139920831582864(-0.1887592802817087,pi/2) q[5];
cx q[3],q[5];
u1(-pi/2) q[3];
s q[3];
h q[3];
s q[3];
u1(pi/2) q[3];
u1(-pi/2) q[5];
u1(3.9619102421886874) q[5];
cx q[6],q[3];
u1(0.6576125944639473) q[3];
cx q[6],q[3];
u1(pi/2) q[3];
u2(0,pi) q[3];
cx q[3],q[1];
cx q[1],q[3];
u2(0,pi) q[1];
u2(0,pi) q[1];
u(5.821204676268721,2.2872367074892246,3.2629429511890864) q[3];
u(0,0,1.1779112118013535) q[3];
u(0,0,3.521289746259811) q[3];
u(0,0,1.6524565951986083) q[6];
u1(0.0525267376898901) q[9];
cx q[8],q[9];
u1(-0.0525267376898901) q[9];
cx q[8],q[9];
u3(0,0,pi) q[8];
cx q[0],q[8];
cx q[8],q[0];
u3(0,0,pi/2) q[8];
cx q[5],q[8];
u(-2.193892271897972,0,0) q[8];
cx q[5],q[8];
u(2.193892271897972,-pi/2,0) q[8];
cx q[6],q[8];
u(0,0,-1.6524565951986083) q[8];
cx q[6],q[8];
u1(-pi/2) q[6];
u2(0,pi) q[6];
u1(-pi/2) q[6];
u(0,0,1.6524565951986083) q[8];
u3(pi/2,0,pi) q[8];
u(0,0,-1.3547601130529334) q[8];
cx q[3],q[8];
u(-1.2821358928786764,0,-3.521289746259811) q[8];
cx q[3],q[8];
u(pi/2,2.1334445443862915,4.8829842162343615) q[3];
u3(pi/2,0,pi) q[3];
u3(0,0,pi/4) q[3];
u(1.2821358928786764,4.876049859312745,0) q[8];
u3(1.7035990605199096,-pi/2,pi/2) q[8];
id q[9];
u2(0,pi) q[9];
cx q[9],q[4];
u1(1.300515940029954) q[4];
cx q[9],q[4];
u2(0,pi) q[4];
cx q[7],q[4];
h q[7];
cx q[4],q[7];
tdg q[7];
cx q[0],q[7];
t q[7];
cx q[4],q[7];
t q[4];
tdg q[7];
cx q[0],q[7];
cx q[0],q[4];
t q[0];
tdg q[4];
cx q[0],q[4];
id q[0];
u3(0,0,pi/2) q[0];
u1(pi/2) q[0];
t q[7];
h q[7];
cx q[7],q[4];
u3(0,0,pi/2) q[4];
cx q[2],q[4];
u(-1.8355420477454554,0,0) q[4];
cx q[2],q[4];
u(1.8355420477454554,-pi/2,0) q[4];
cx q[4],q[1];
u2(0,pi) q[1];
id q[4];
u(6.153294093689496,3.36392268582221,-3.36392268582221) q[4];
u3(0,0,pi/2) q[7];
cx q[5],q[7];
u(-1.7014975122835039,0,0) q[7];
cx q[5],q[7];
u3(0,0,2.2247874592843964) q[5];
cx q[5],q[2];
u3(0,0,-2.2247874592843964) q[2];
cx q[5],q[2];
u3(0,0,2.2247874592843964) q[2];
reset q[2];
u1(pi/2) q[2];
u2(0,pi) q[2];
u1(pi/2) q[2];
u(0,0,3.4897589915702274) q[2];
u1(-pi/2) q[2];
sdg q[2];
h q[2];
sdg q[2];
u1(pi/2) q[2];
u3(pi/2,0,pi) q[5];
u3(0,0,pi/4) q[5];
cx q[6],q[5];
u3(0,0,-pi/4) q[5];
u3(pi/2,0,pi) q[5];
u(1.7014975122835039,-pi/2,0) q[7];
h q[7];
u2(0,pi) q[9];
u(pi,pi/2,pi/2) q[9];
u3(0,0,pi/2) q[9];
cx q[9],q[7];
rz(-pi/4) q[7];
cx q[9],q[7];
h q[7];
h q[7];
u3(pi,0,pi) q[9];
cx q[9],q[7];
rz(-pi/4) q[7];
cx q[9],q[7];
h q[7];
u1(pi/2) q[7];
u2(0,pi) q[7];
cx q[7],q[0];
cx q[0],q[7];
u2(0,pi) q[0];
u(0,0,4.446444790900819) q[7];
u(0,0,4.019784882375806) q[7];
cx q[9],q[5];
u3(0,0,pi/4) q[5];
cx q[1],q[5];
u3(0,0,-pi/4) q[5];
cx q[9],q[5];
u3(0,0,pi/4) q[5];
cx q[1],q[5];
cx q[0],q[1];
h q[0];
cx q[1],q[0];
tdg q[0];
u3(0,0,-pi/4) q[5];
u3(pi/2,0,pi) q[5];
u3(0,0,pi/4) q[5];
cx q[6],q[5];
u3(0,0,-pi/4) q[5];
u3(pi/2,0,pi) q[5];
u3(0.38421019136353923,-pi/2,pi/2) q[5];
cx q[6],q[0];
t q[0];
cx q[1],q[0];
tdg q[0];
t q[1];
cx q[6],q[0];
t q[0];
h q[0];
cx q[6],q[1];
tdg q[1];
t q[6];
cx q[6],q[1];
cx q[0],q[1];
cx q[1],q[3];
u3(0,0,-pi/4) q[3];
u3(pi/2,0,pi) q[3];
cx q[8],q[3];
u3(0,0,pi/4) q[3];
cx q[6],q[3];
u3(0,0,-pi/4) q[3];
cx q[8],q[3];
u3(0,0,pi/4) q[3];
cx q[6],q[3];
u3(0,0,-pi/4) q[3];
u3(pi/2,0,pi) q[3];
u3(0,0,pi/4) q[3];
cx q[1],q[3];
u(pi,pi/2,pi/2) q[1];
u3(0,0,4.39521995921761) q[1];
u3(0,0,-pi/4) q[3];
u3(pi/2,0,pi) q[3];
u(0,0,-0.38377274660548255) q[9];
cx q[7],q[9];
u(-0.8176429474634271,0,-4.019784882375806) q[9];
cx q[7],q[9];
u3(pi/2,0,pi) q[7];
u3(0,0,pi/4) q[7];
cx q[0],q[7];
u3(0,0,-pi/4) q[7];
cx q[4],q[7];
cx q[3],q[4];
u1(3.4888724096580606) q[4];
cx q[3],q[4];
r_139920833955856(0.3545035235854771,pi/2) q[4];
u3(0,0,pi/4) q[7];
cx q[0],q[7];
cx q[0],q[6];
u2(0,pi) q[0];
cx q[3],q[0];
u1(0.6596560662519005) q[0];
cx q[3],q[0];
u2(0,pi) q[0];
u1(pi/4) q[3];
u3(pi/2,0,pi) q[6];
u3(0,0,-pi/4) q[7];
u3(pi/2,0,pi) q[7];
u1(2.4340686080990186) q[7];
u1(pi/2) q[7];
cx q[2],q[7];
r_139920833961232(-1.9020843457228387,pi/2) q[2];
r_139920833961552(-1.9020843457228387,pi/2) q[7];
cx q[2],q[7];
u1(-pi/2) q[2];
s q[2];
h q[2];
s q[2];
u1(pi/2) q[2];
cx q[2],q[4];
r_139920833964048(-0.3545035235854771,pi/2) q[4];
cx q[2],q[4];
u3(pi/2,0,pi) q[2];
u(pi,pi/2,pi/2) q[2];
u1(pi/2) q[2];
u2(0,pi) q[2];
u1(pi/4) q[2];
u3(0,0,-pi/2) q[4];
u1(-pi/2) q[7];
u1(-2.4340686080990186) q[7];
u(pi,pi/2,pi/2) q[7];
cx q[7],q[6];
cx q[6],q[7];
u(4.804756136159626,2.914017770069889,1.1820469263732427) q[7];
u2(0,pi) q[7];
u(0.8176429474634271,4.4035576289812886,0) q[9];
u(0,0,2.5574363495477144) q[9];
cx q[9],q[5];
u(0,0,-2.5574363495477144) q[5];
cx q[9],q[5];
u(0,0,2.5574363495477144) q[5];
u2(0,pi) q[5];
cx q[8],q[5];
u1(4.707768480530489) q[5];
cx q[8],q[5];
u2(0,pi) q[5];
reset q[5];
cx q[5],q[0];
cx q[0],q[5];
cx q[5],q[0];
u(0.7042637073062884,0.9069805345148946,0.8924150759041883) q[0];
u2(0,pi) q[0];
u3(2.324831619705996,0,0) q[5];
cx q[5],q[2];
u1(-pi/4) q[2];
u2(0,pi) q[2];
u1(-pi/2) q[2];
u1(pi/2) q[2];
u2(0,pi) q[2];
u(0,0,-pi/4) q[5];
cx q[7],q[0];
u1(3.3076714063417967) q[0];
cx q[7],q[0];
u2(0,pi) q[0];
cx q[5],q[0];
u(0,0,pi/4) q[0];
cx q[5],q[0];
u(0,0,-pi/4) q[0];
u3(3.4835716497483213,-pi/2,pi/2) q[0];
u(4.921326621750277,3.838953572272524,6.24969412670182) q[0];
u3(0,0,-pi/2) q[0];
u3(0,0,5.986053912685008) q[0];
r_139920833602768(pi/2,0) q[0];
u3(0,0,-pi/2) q[5];
u1(pi/4) q[5];
u2(0,pi) q[7];
u3(pi/2,0,pi) q[7];
u3(0,0,pi) q[7];
u3(0,0,pi/4) q[7];
u(pi,0,pi) q[7];
u(pi,0,pi) q[7];
u3(0,0,pi) q[7];
r_139920833598160(pi/2,0) q[7];
cx q[0],q[7];
u1(4.147249151983034) q[7];
cx q[0],q[7];
r_139920833601360(-pi/2,0) q[0];
r_139920833608976(-pi/2,0) q[7];
u1(pi/2) q[8];
u2(0,pi) q[8];
u1(pi/2) q[8];
id q[8];
cx q[8],q[6];
cx q[6],q[8];
u1(pi/2) q[6];
u2(0,pi) q[6];
u1(pi/2) q[6];
u(pi,0,pi) q[8];
u(0,0,3.167917527071259) q[8];
id q[9];
u3(0,0,-0.22632357357033062) q[9];
cx q[1],q[9];
u(-2.900332387615164,0,-4.39521995921761) q[9];
cx q[1],q[9];
u2(0,pi) q[1];
cx q[3],q[1];
u1(-pi/4) q[1];
cx q[3],q[1];
u1(pi/4) q[1];
u2(0,pi) q[1];
u2(0,pi) q[3];
cx q[1],q[3];
u1(-pi/4) q[3];
cx q[4],q[3];
u1(pi/4) q[3];
cx q[1],q[3];
u1(pi/4) q[1];
u1(-pi/4) q[3];
cx q[4],q[3];
u1(pi/4) q[3];
u2(0,pi) q[3];
u1(4.146238471858078) q[3];
u1(pi/2) q[3];
cx q[4],q[1];
u1(-pi/4) q[1];
u1(pi/4) q[4];
cx q[4],q[1];
u1(-pi/2) q[1];
sdg q[1];
h q[1];
sdg q[1];
u1(pi/2) q[1];
cx q[1],q[3];
r_139920833504208(-0.6001541715126423,pi/2) q[1];
r_139920833498256(-0.6001541715126423,pi/2) q[3];
cx q[1],q[3];
u1(-pi/2) q[1];
s q[1];
h q[1];
s q[1];
u1(pi/2) q[1];
u2(0,pi) q[1];
u1(-pi/2) q[3];
u1(-4.146238471858078) q[3];
cx q[3],q[1];
u1(-pi/4) q[1];
u3(0,0,pi/4) q[4];
u(pi,0,pi) q[4];
cx q[6],q[1];
u1(pi/4) q[1];
cx q[3],q[1];
u1(-pi/4) q[1];
u1(pi/4) q[3];
cx q[6],q[1];
u1(pi/4) q[1];
u2(0,pi) q[1];
u1(pi/2) q[1];
u2(0,pi) q[1];
u1(pi/4) q[1];
cx q[6],q[3];
u1(-pi/4) q[3];
u1(pi/4) q[6];
cx q[6],q[3];
u3(0,0,pi/2) q[3];
cx q[4],q[3];
u(-1.996863907399938,0,0) q[3];
cx q[4],q[3];
u(1.996863907399938,-pi/2,0) q[3];
u2(0,pi) q[3];
u2(0,pi) q[4];
cx q[5],q[3];
u1(-pi/4) q[3];
cx q[5],q[3];
u1(pi/4) q[3];
u2(0,pi) q[3];
u(pi/2,2.868434341348226,0.945613003464723) q[5];
u1(pi/4) q[5];
u(5.0693150186823726,1.279964889556483,5.604989781757752) q[6];
reset q[6];
cx q[6],q[4];
u2(0,pi) q[4];
u3(1.51050062325737,-pi/2,pi/2) q[4];
u3(pi/2,0,pi) q[4];
u(2.900332387615164,4.621543532787941,0) q[9];
u3(0.9395963062134063,-pi/2,pi/2) q[9];
u1(pi/2) q[9];
u2(0,pi) q[9];
u1(pi/2) q[9];
u(1.2528187658507035,5.025741021176934,3.806010232797124) q[9];
u1(pi/2) q[9];
cx q[2],q[9];
cx q[9],q[2];
cx q[8],q[2];
cx q[2],q[8];
cx q[8],q[2];
u3(pi/2,0,pi) q[2];
u3(0,0,3.397037105271456) q[2];
u3(0,0,pi/4) q[8];
u2(0,pi) q[8];
cx q[3],q[8];
u1(1.409930183391899) q[8];
cx q[3],q[8];
cx q[6],q[3];
cx q[3],q[6];
cx q[6],q[3];
r_139920833440144(pi/2,0) q[3];
id q[6];
u3(0,0,-pi/4) q[6];
u2(0,pi) q[8];
u3(0,0,0.3408986579711297) q[8];
cx q[2],q[8];
u(-0.5149620543941036,0,-3.397037105271456) q[8];
cx q[2],q[8];
u(0.5149620543941036,3.0561384473003264,0) q[8];
u1(pi/2) q[8];
u2(0,pi) q[8];
u1(pi/2) q[8];
reset q[8];
u(0,0,pi/4) q[8];
u2(0,pi) q[9];
cx q[9],q[1];
u1(-pi/4) q[1];
u2(0,pi) q[1];
u1(-pi/2) q[1];
u3(0,0,pi) q[1];
u(pi,pi/2,pi/2) q[1];
u(2.762035314183364,3.8119236820979454,-3.8119236820979454) q[1];
u2(0,pi) q[1];
cx q[2],q[1];
u2(0,pi) q[1];
u3(0,0,pi/2) q[2];
cx q[4],q[1];
u1(1.6361276803289553) q[1];
cx q[4],q[1];
u2(0,pi) q[1];
u1(pi/2) q[4];
u2(0,pi) q[4];
u1(pi/2) q[4];
cx q[7],q[1];
u1(-pi/4) q[1];
cx q[8],q[2];
u(0,0,-pi/4) q[2];
cx q[8],q[2];
u(0,0,pi/4) q[2];
u3(0,0,pi/2) q[2];
u2(0,pi) q[2];
cx q[8],q[4];
cx q[4],q[8];
cx q[8],q[4];
u3(3.7784291202132083,0,0) q[4];
u(pi/2,0.544087473513986,5.820530626384081) q[4];
r_139920833285584(pi/2,0) q[8];
u(pi,pi/2,pi/2) q[9];
u2(0,pi) q[9];
cx q[5],q[9];
u1(-pi/4) q[9];
cx q[5],q[9];
u(0,0,6.188639322726221) q[5];
u3(0,0,pi) q[5];
cx q[5],q[1];
u1(pi/4) q[1];
cx q[7],q[1];
u1(-pi/4) q[1];
cx q[5],q[1];
u1(pi/4) q[1];
u2(0,pi) q[1];
u3(0,0,1.5987426426675897) q[1];
u1(pi/4) q[7];
cx q[5],q[7];
u1(pi/4) q[5];
u1(-pi/4) q[7];
cx q[5],q[7];
u(1.8261010815427707,4.859401440181732,3.342155825622948) q[7];
u1(pi/4) q[7];
cx q[7],q[2];
u1(-pi/4) q[2];
cx q[7],q[2];
u1(pi/4) q[2];
u2(0,pi) q[2];
u3(0,0,pi/2) q[2];
u3(0,0,pi/2) q[7];
u1(pi/4) q[9];
u2(0,pi) q[9];
r_139920833290064(pi/2,0) q[9];
cx q[3],q[9];
u1(6.136556970858255) q[9];
cx q[3],q[9];
r_139920833260432(-pi/2,0) q[3];
u3(0,0,pi) q[3];
cx q[3],q[0];
cx q[0],q[3];
cx q[3],q[0];
u3(pi/2,0,pi) q[0];
u3(0,0,3.9138371829351533) q[3];
cx q[3],q[1];
u(-0.11085572217063448,0,-3.9138371829351533) q[1];
cx q[3],q[1];
u(0.11085572217063448,2.315094540267564,0) q[1];
u3(pi/2,0,pi) q[1];
u3(0,0,pi/4) q[3];
u3(0,0,-pi/2) q[3];
r_139920833260880(-pi/2,0) q[9];
u(5.808155601268253,3.73051355556753,-3.73051355556753) q[9];
u(0,0,pi/4) q[9];
cx q[9],q[6];
u(0,0,-pi/4) q[6];
cx q[9],q[6];
u(0,0,pi/4) q[6];
cx q[6],q[5];
cx q[5],q[6];
cx q[0],q[5];
u1(1.5294106812130643) q[5];
cx q[0],q[5];
u3(0,0,pi) q[0];
u3(5.448109782387749,-pi/2,pi/2) q[5];
u3(0,0,pi/2) q[6];
cx q[6],q[7];
u(-2.592642799024121,0,0) q[7];
cx q[6],q[7];
u(2.592642799024121,-pi/2,0) q[7];
u3(0,0,-pi/4) q[9];
r_139920833264720(pi/2,0) q[9];
cx q[8],q[9];
u1(4.932279470860425) q[9];
cx q[8],q[9];
r_139920833254160(-pi/2,0) q[8];
id q[8];
r_139920833261328(-pi/2,0) q[9];
u(0,0,-pi/4) q[9];
cx q[9],q[1];
u(0,0,pi/4) q[1];
cx q[9],q[1];
u(0,0,-pi/4) q[1];
