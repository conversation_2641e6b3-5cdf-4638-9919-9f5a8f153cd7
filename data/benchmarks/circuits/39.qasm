OPENQASM 2.0;
include "qelib1.inc";
gate r(param0,param1) q0 { u3(1.286438676683018,0,0) q0; }
gate r_139920834822032(param0,param1) q0 { u3(-1.286438676683018,0,0) q0; }
gate r_139920838263376(param0,param1) q0 { u3(0.9825456164635477,0,0) q0; }
gate r_139920838263824(param0,param1) q0 { u3(-0.9825456164635477,0,0) q0; }
gate r_139920838246608(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920835014672(param0,param1) q0 { u3(2.658682207354401,0,0) q0; }
gate r_139920834713680(param0,param1) q0 { u3(-2.658682207354401,0,0) q0; }
gate r_139920834129552(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920834119888(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920834123664(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920834123472(param0,param1) q0 { u3(-1.4128628016100828,0,0) q0; }
gate r_139920834124176(param0,param1) q0 { u3(1.4128628016100828,0,0) q0; }
gate r_139920837515472(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920830573072(param0,param1) q0 { u3(0.1675073491866987,0,0) q0; }
gate r_139920830754960(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920830750096(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920830483728(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920830599056(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920830607312(param0,param1) q0 { u3(1.5057363830106196,0,0) q0; }
gate r_139920830608016(param0,param1) q0 { u3(-1.5057363830106196,0,0) q0; }
gate r_139920830154448(param0,param1) q0 { u3(-0.1675073491866987,0,0) q0; }
gate r_139920830156176(param0,param1) q0 { u3(-1.5226808922074992,0,0) q0; }
gate r_139920830156496(param0,param1) q0 { u3(-1.5226808922074992,0,0) q0; }
gate r_139920830158864(param0,param1) q0 { u3(-2.1401656596073106,0,0) q0; }
gate r_139920830159184(param0,param1) q0 { u3(-2.1401656596073106,0,0) q0; }
gate r_139920830192400(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920830188944(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920830762704(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920830763600(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920830948944(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920830950352(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920830950672(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
qreg q[10];
u2(0,pi) q[3];
cx q[1],q[4];
h q[1];
cx q[4],q[1];
tdg q[1];
cx q[5],q[1];
t q[1];
cx q[4],q[1];
tdg q[1];
t q[4];
cx q[5],q[1];
t q[1];
h q[1];
cx q[5],q[4];
tdg q[4];
t q[5];
cx q[5],q[4];
cx q[1],q[4];
r(1.286438676683018,pi/2) q[1];
h q[6];
cx q[2],q[6];
rz(-pi/4) q[6];
cx q[2],q[6];
u3(pi,0,pi) q[2];
h q[6];
h q[6];
cx q[2],q[6];
rz(-pi/4) q[6];
cx q[2],q[6];
cx q[2],q[5];
cx q[5],q[2];
u1(-pi/2) q[2];
u2(0,pi) q[2];
u1(-pi/2) q[2];
u(0,0,-pi/4) q[2];
u3(0,0,pi/2) q[5];
h q[6];
u1(pi/4) q[7];
cx q[7],q[3];
u1(-pi/4) q[3];
cx q[7],q[3];
u1(pi/4) q[3];
u2(0,pi) q[3];
cx q[8],q[0];
u1(1.9935830826672294) q[0];
cx q[8],q[0];
u3(pi/2,0,pi) q[8];
u3(0,0,pi/4) q[8];
cx q[0],q[8];
u3(0,0,-pi/4) q[8];
u3(pi/2,0,pi) q[8];
cx q[4],q[8];
u3(0,0,pi/4) q[8];
cx q[3],q[8];
u3(0,0,-pi/4) q[8];
cx q[4],q[8];
cx q[4],q[6];
u1(3.9366361492043325) q[6];
cx q[4],q[6];
u2(0,pi) q[6];
u3(0,0,pi/4) q[8];
cx q[3],q[8];
cx q[3],q[1];
r_139920834822032(-1.286438676683018,pi/2) q[1];
cx q[3],q[1];
u(0.9305492109076575,0.019206118942627803,-0.019206118942627803) q[1];
u(pi,0,pi) q[1];
u3(0,0,2.649250444168787) q[1];
u1(-pi/2) q[3];
cx q[4],q[3];
u1(pi/2) q[3];
cx q[2],q[3];
u(0,0,pi/4) q[3];
cx q[2],q[3];
u(0,0,-pi/4) q[3];
reset q[4];
u3(0,0,0.6406349125025554) q[4];
cx q[4],q[2];
u3(0,0,-0.6406349125025554) q[2];
cx q[4],q[2];
u3(0,0,0.6406349125025554) q[2];
u2(0,pi) q[4];
u3(0,0,-pi/4) q[8];
u3(pi/2,0,pi) q[8];
u3(0,0,pi/4) q[8];
cx q[0],q[8];
u1(1.3167391428868414) q[0];
u3(0,0,-pi/4) q[8];
u3(pi/2,0,pi) q[8];
u1(pi/2) q[8];
u2(0,pi) q[8];
u1(pi/2) q[8];
u1(pi/4) q[8];
cx q[8],q[6];
u1(-pi/4) q[6];
cx q[8],q[6];
u1(pi/4) q[6];
u2(0,pi) q[6];
u1(pi/2) q[6];
u2(0,pi) q[6];
u1(pi/4) q[6];
u3(0,0,pi/2) q[9];
cx q[7],q[9];
cx q[9],q[7];
cx q[7],q[9];
u(pi/2,2.5526069322072455,4.0514552733073135) q[7];
cx q[7],q[5];
u(-0.7187266015716858,0,0) q[5];
cx q[7],q[5];
u(0.7187266015716858,-pi/2,0) q[5];
u1(-4.439762391522942) q[5];
u1(pi/2) q[5];
u1(1.2798375987102997) q[7];
cx q[8],q[7];
u1(-1.2798375987102997) q[7];
cx q[8],q[7];
u1(pi/2) q[7];
u2(0,pi) q[7];
h q[8];
cx q[3],q[8];
rz(-pi/4) q[8];
cx q[3],q[8];
u3(pi,0,pi) q[3];
h q[8];
h q[8];
cx q[3],q[8];
rz(-pi/4) q[8];
cx q[3],q[8];
h q[3];
h q[8];
u2(0,pi) q[8];
cx q[9],q[0];
u1(-1.3167391428868414) q[0];
cx q[9],q[0];
u3(0,0,pi/2) q[9];
cx q[0],q[9];
u(-0.7739635077500095,0,0) q[9];
cx q[0],q[9];
u1(-pi/2) q[0];
sdg q[0];
h q[0];
sdg q[0];
u1(pi/2) q[0];
cx q[0],q[5];
r_139920838263376(0.9825456164635477,pi/2) q[0];
r_139920838263824(-0.9825456164635477,pi/2) q[5];
cx q[0],q[5];
u1(-pi/2) q[0];
s q[0];
h q[0];
s q[0];
u1(pi/2) q[0];
u3(0,0,-1.7190064442232305) q[0];
cx q[1],q[0];
u(-2.941410168697438,0,-2.649250444168787) q[0];
cx q[1],q[0];
u(2.941410168697438,4.368256888392017,0) q[0];
u1(pi/4) q[0];
cx q[0],q[4];
cx q[1],q[8];
u1(-pi/4) q[4];
cx q[0],q[4];
u3(0,0,pi/2) q[0];
cx q[1],q[0];
u(-0.340796817422504,0,0) q[0];
cx q[1],q[0];
u(0.340796817422504,-pi/2,0) q[0];
u(pi/2,0.6454533187538802,4.771557897617236) q[0];
u2(0,pi) q[1];
u1(pi/4) q[4];
u2(0,pi) q[4];
u1(-pi/2) q[5];
u1(4.439762391522942) q[5];
u3(0,0,0.27909975670624965) q[5];
u2(0,pi) q[8];
u(pi,0,pi) q[8];
u(0.7739635077500095,-pi/2,0) q[9];
cx q[9],q[6];
u1(-pi/4) q[6];
u2(0,pi) q[6];
u1(-pi/2) q[6];
u3(0,0,5.502597918754978) q[6];
cx q[6],q[5];
u(-0.13089229555402554,0,-5.502597918754978) q[5];
cx q[6],q[5];
u(0.13089229555402554,5.223498162048728,0) q[5];
cx q[5],q[3];
rz(-pi/4) q[3];
cx q[5],q[3];
h q[3];
h q[3];
u3(pi,0,pi) q[5];
cx q[5],q[3];
rz(-pi/4) q[3];
cx q[5],q[3];
h q[3];
u(pi,0,pi) q[5];
u(1.1150140848479893,-0.811137272278874,0.811137272278874) q[6];
u3(2.425907445839842,-pi/2,pi/2) q[6];
u3(3.473406729104485,0,0) q[6];
h q[6];
cx q[0],q[6];
rz(-pi/4) q[6];
cx q[0],q[6];
u3(pi,0,pi) q[0];
h q[6];
h q[6];
cx q[0],q[6];
rz(-pi/4) q[6];
cx q[0],q[6];
u2(0,pi) q[0];
h q[6];
u1(pi/2) q[9];
cx q[7],q[9];
cx q[9],q[7];
u1(2.9346425553004187) q[7];
cx q[2],q[7];
u1(-2.9346425553004187) q[7];
cx q[2],q[7];
u2(0,pi) q[7];
cx q[3],q[7];
u1(-pi/4) q[7];
cx q[4],q[7];
u1(pi/4) q[7];
cx q[3],q[7];
u1(pi/4) q[3];
u1(-pi/4) q[7];
cx q[4],q[7];
cx q[4],q[3];
u1(-pi/4) q[3];
u1(pi/4) q[4];
cx q[4],q[3];
h q[3];
cx q[4],q[5];
cx q[5],q[4];
u(pi,0,pi) q[4];
u2(0,pi) q[4];
u(4.390342681986343,3.790521732548662,2.5552569132839307) q[5];
cx q[5],q[4];
u2(0,pi) q[4];
r_139920838246608(pi/2,0) q[4];
u1(pi/4) q[7];
u2(0,pi) q[7];
u(pi/2,5.377964114955566,4.493853069065953) q[7];
u(0.8803883711251335,2.9662993315508936,-2.9662993315508936) q[7];
u(0,0,6.118699962033441) q[7];
u3(0,0,-pi/2) q[7];
cx q[8],q[3];
rz(-pi/4) q[3];
cx q[8],q[3];
h q[3];
h q[3];
u3(pi,0,pi) q[8];
cx q[8],q[3];
rz(-pi/4) q[3];
cx q[8],q[3];
h q[3];
u2(0,pi) q[3];
u1(pi/4) q[8];
cx q[8],q[3];
u1(-pi/4) q[3];
cx q[8],q[3];
u1(pi/4) q[3];
u2(0,pi) q[3];
u1(pi/2) q[3];
u2(0,pi) q[3];
u1(pi/4) q[3];
cx q[6],q[3];
u1(-pi/4) q[3];
u2(0,pi) q[3];
u1(-pi/2) q[3];
u(0,0,6.1059848442091) q[6];
u2(0,pi) q[6];
cx q[7],q[6];
u2(0,pi) q[6];
u3(0,0,2.4010754415417854) q[7];
u2(0,pi) q[8];
cx q[0],q[8];
u1(0.6463585159836212) q[8];
cx q[0],q[8];
u2(0,pi) q[0];
u2(0,pi) q[8];
u3(pi/2,0,pi) q[8];
u3(0,0,pi/4) q[8];
cx q[5],q[8];
u3(0,0,-pi/4) q[8];
u3(pi/2,0,pi) q[8];
cx q[3],q[8];
u3(0,0,pi/4) q[8];
cx q[0],q[8];
u3(0,0,-pi/4) q[8];
cx q[3],q[8];
u3(0,0,pi/4) q[8];
cx q[0],q[8];
u3(0,0,-pi/4) q[8];
u3(pi/2,0,pi) q[8];
u3(0,0,pi/4) q[8];
cx q[5],q[8];
cx q[5],q[3];
cx q[3],q[5];
u3(0,0,pi/4) q[5];
r_139920835014672(2.658682207354401,pi/2) q[5];
cx q[7],q[5];
r_139920834713680(-2.658682207354401,pi/2) q[5];
cx q[7],q[5];
u3(0,0,3.8185720264340537) q[5];
u3(0,0,-pi/4) q[8];
u3(pi/2,0,pi) q[8];
u2(0,pi) q[9];
u3(0,0,pi/4) q[9];
cx q[2],q[9];
cx q[9],q[2];
u3(0,0,2.7621788177771283) q[2];
u(pi/2,1.9034721391906295,3.200303416701119) q[2];
u(0,0,3.9704386111836216) q[2];
r_139920834129552(pi/2,0) q[2];
cx q[4],q[2];
u1(1.9428253816608032) q[2];
cx q[4],q[2];
r_139920834119888(-pi/2,0) q[2];
cx q[2],q[8];
h q[2];
r_139920834123664(-pi/2,0) q[4];
cx q[0],q[4];
h q[0];
cx q[4],q[0];
tdg q[0];
cx q[8],q[2];
tdg q[2];
cx q[9],q[1];
u1(1.175947752948776) q[1];
cx q[9],q[1];
u2(0,pi) q[1];
u3(0,0,-pi/4) q[1];
u1(-5.301282575710273) q[1];
u1(pi/2) q[1];
u3(0,0,pi/4) q[9];
u1(-pi/2) q[9];
sdg q[9];
h q[9];
sdg q[9];
u1(pi/2) q[9];
cx q[9],q[1];
r_139920834123472(-1.4128628016100828,pi/2) q[1];
r_139920834124176(1.4128628016100828,pi/2) q[9];
cx q[9],q[1];
u1(-pi/2) q[1];
u1(5.301282575710273) q[1];
u3(0,0,pi/2) q[1];
cx q[1],q[0];
t q[0];
cx q[4],q[0];
tdg q[0];
cx q[1],q[0];
t q[0];
h q[0];
t q[4];
cx q[1],q[4];
t q[1];
tdg q[4];
cx q[1],q[4];
cx q[0],q[4];
u3(0,0,1.6581432916900705) q[0];
u(0,0,5.634090765352861) q[0];
id q[0];
u(pi/2,0.2553000684982186,5.429878622137461) q[0];
u1(pi/2) q[0];
u2(0,pi) q[0];
u1(pi/4) q[0];
r_139920837515472(pi/2,0) q[1];
u2(0,pi) q[4];
cx q[6],q[4];
u1(-pi/4) q[4];
cx q[3],q[4];
u1(pi/4) q[4];
cx q[6],q[4];
u1(-pi/4) q[4];
cx q[3],q[4];
u1(pi/4) q[4];
u2(0,pi) q[4];
u1(pi/4) q[6];
cx q[3],q[6];
u1(pi/4) q[3];
u1(-pi/4) q[6];
cx q[3],q[6];
u(2.359344851002238,0.4102123656969129,0.838472104821513) q[6];
u(pi,pi/2,pi/2) q[6];
u(0.17928797254042775,3.7346964575656947,3.54688271795077) q[6];
u(6.164120857256323,3.4815936356807646,3.658402537784863) q[6];
r_139920830573072(0.1675073491866987,pi/2) q[6];
u1(-pi/2) q[9];
s q[9];
h q[9];
s q[9];
u1(pi/2) q[9];
id q[9];
cx q[9],q[2];
t q[2];
cx q[8],q[2];
tdg q[2];
t q[8];
cx q[9],q[2];
t q[2];
h q[2];
cx q[9],q[8];
tdg q[8];
t q[9];
cx q[9],q[8];
cx q[2],q[8];
u(0.8440412053628448,2.9198377595590044,2.3444907380588904) q[2];
cx q[2],q[3];
u1(1.3132517266742882) q[3];
cx q[2],q[3];
u(0,0,2.7491850517711014) q[3];
u(0,0,pi/4) q[3];
cx q[3],q[7];
u(0,0,-pi/4) q[7];
cx q[3],q[7];
u(0,0,0.8082920747625245) q[3];
u(0,0,1.3706024837290287) q[3];
u(0,0,pi/4) q[7];
u(0,0,3.217135966579552) q[7];
u(0,0,2.1608720427465298) q[7];
r_139920830754960(pi/2,0) q[8];
cx q[8],q[1];
u1(2.9495618420157066) q[1];
cx q[8],q[1];
r_139920830750096(-pi/2,0) q[1];
u(pi,0,pi) q[1];
u3(0,0,0.9220853672099241) q[1];
cx q[5],q[1];
u(-0.6384610901711475,0,-3.8185720264340537) q[1];
cx q[5],q[1];
u(0.6384610901711475,2.8964866592241294,0) q[1];
u(0,0,0.2894620802119431) q[1];
cx q[3],q[1];
u(-2.1515858841944526,0,-1.3706024837290287) q[1];
cx q[3],q[1];
u(2.1515858841944526,1.0811404035170855,0) q[1];
u3(0,0,pi/2) q[1];
u1(pi/2) q[3];
u2(0,pi) q[3];
u1(pi/4) q[3];
cx q[5],q[0];
u1(-pi/4) q[0];
u2(0,pi) q[0];
u1(-pi/2) q[0];
u3(0,0,-0.019282164777450747) q[0];
u2(0,pi) q[5];
r_139920830483728(-pi/2,0) q[8];
u(pi,0,pi) q[9];
u2(0,pi) q[9];
h q[9];
cx q[4],q[9];
tdg q[9];
cx q[8],q[9];
t q[9];
cx q[4],q[9];
t q[4];
tdg q[9];
cx q[8],q[9];
cx q[8],q[4];
tdg q[4];
t q[8];
cx q[8],q[4];
cx q[4],q[8];
h q[4];
cx q[8],q[4];
tdg q[4];
cx q[2],q[4];
t q[4];
cx q[8],q[4];
tdg q[4];
cx q[2],q[4];
t q[4];
h q[4];
t q[8];
cx q[2],q[8];
t q[2];
tdg q[8];
cx q[2],q[8];
u3(0,0,pi/2) q[2];
cx q[4],q[8];
cx q[4],q[2];
u(-2.143052512437402,0,0) q[2];
cx q[4],q[2];
u(2.143052512437402,-pi/2,0) q[2];
u(pi/2,3.8574474862255936,2.0507305303468883) q[2];
cx q[2],q[3];
u1(-pi/4) q[3];
u2(0,pi) q[3];
u1(-pi/2) q[3];
u2(0,pi) q[3];
u(3.2860551442637074,-0.15944370072520941,0.15944370072520941) q[4];
cx q[4],q[1];
u(-0.7282934608418898,0,0) q[1];
cx q[4],q[1];
u(0.7282934608418898,-pi/2,0) q[1];
cx q[1],q[3];
u1(0.7607117802688796) q[3];
cx q[1],q[3];
r_139920830599056(pi/2,0) q[1];
u2(0,pi) q[3];
u(pi,0,pi) q[3];
u(pi,0,pi) q[3];
u3(pi/2,0,pi) q[4];
u1(-pi/2) q[4];
sdg q[4];
h q[4];
sdg q[4];
u1(pi/2) q[4];
u2(0,pi) q[8];
t q[9];
h q[9];
u2(0,pi) q[9];
u3(0,0,pi) q[9];
u2(0,pi) q[9];
cx q[9],q[8];
u1(0.2135692083743144) q[8];
cx q[9],q[8];
u2(0,pi) q[8];
u(0,0,-0.5702404932869888) q[8];
cx q[7],q[8];
u(-0.7135866997113716,0,-2.1608720427465298) q[8];
cx q[7],q[8];
cx q[7],q[5];
u2(0,pi) q[5];
r_139920830607312(1.5057363830106196,pi/2) q[7];
cx q[2],q[7];
r_139920830608016(-1.5057363830106196,pi/2) q[7];
cx q[2],q[7];
u3(0,0,4.006303092044377) q[2];
u1(-pi/2) q[2];
sdg q[2];
h q[2];
sdg q[2];
u1(pi/2) q[2];
u3(0,0,0.0005916203305113066) q[7];
u(0.7135866997113716,2.7311125360335184,0) q[8];
u3(0,0,0.10513784415432909) q[8];
cx q[8],q[0];
u(-2.059412978186287,0,-0.10513784415432909) q[0];
cx q[8],q[0];
u(2.059412978186287,0.12442000893177983,0) q[0];
cx q[0],q[6];
r_139920830154448(-0.1675073491866987,pi/2) q[6];
cx q[0],q[6];
u1(3.8910776911257425) q[0];
u1(pi/2) q[0];
cx q[4],q[0];
r_139920830156176(-1.5226808922074992,pi/2) q[0];
r_139920830156496(-1.5226808922074992,pi/2) q[4];
cx q[4],q[0];
u1(-pi/2) q[0];
u1(-3.8910776911257425) q[0];
u1(3.2887922444392985) q[0];
u1(pi/2) q[0];
cx q[2],q[0];
r_139920830158864(-2.1401656596073106,pi/2) q[0];
r_139920830159184(-2.1401656596073106,pi/2) q[2];
cx q[2],q[0];
u1(-pi/2) q[0];
u1(-3.2887922444392985) q[0];
u(1.950215111696401,0.2186248273108129,0.5789797485552896) q[0];
u1(pi/4) q[0];
u1(-pi/2) q[2];
s q[2];
h q[2];
s q[2];
u1(pi/2) q[2];
u1(-pi/2) q[4];
s q[4];
h q[4];
s q[4];
u1(pi/2) q[4];
u(5.727928214362138,3.5975672569523676,1.0790439160628076) q[8];
reset q[8];
u(4.031950189754299,6.180644407912511,5.07564626791568) q[8];
u2(0,pi) q[9];
u3(1.2259014265709853,-pi/2,pi/2) q[9];
u(pi,0,pi) q[9];
u(0,0,pi/4) q[9];
cx q[9],q[5];
u(0,0,-pi/4) q[5];
cx q[9],q[5];
u(0,0,pi/4) q[5];
cx q[5],q[6];
u(0,0,pi/4) q[5];
cx q[5],q[7];
u(pi,0,pi) q[6];
u(0,0,-pi/4) q[7];
cx q[5],q[7];
u1(pi/2) q[5];
u(0,0,pi/4) q[7];
u3(pi/2,0,pi) q[7];
u3(0,0,pi/4) q[7];
cx q[8],q[6];
cx q[6],q[8];
u(pi/2,5.376873982780832,3.2394792252646707) q[6];
u3(0,0,-pi/4) q[6];
h q[8];
r_139920830192400(pi/2,0) q[9];
cx q[9],q[1];
u1(3.95713558116232) q[1];
cx q[9],q[1];
r_139920830188944(-pi/2,0) q[1];
u(0,0,1.6232460251443268) q[1];
cx q[1],q[4];
u(0,0,-1.6232460251443268) q[4];
cx q[1],q[4];
cx q[1],q[7];
u(0,0,1.6232460251443268) q[4];
u1(pi/2) q[4];
u2(0,pi) q[4];
cx q[4],q[5];
cx q[5],q[4];
u2(0,pi) q[4];
cx q[0],q[4];
u1(-pi/4) q[4];
cx q[0],q[4];
u3(1.6529412258613763,-pi/2,pi/2) q[0];
u1(pi/4) q[4];
u2(0,pi) q[4];
u2(0,pi) q[5];
u2(0,pi) q[5];
u3(0,0,-pi/4) q[7];
cx q[2],q[7];
u2(0,pi) q[2];
cx q[5],q[2];
u1(3.5555306845703196) q[2];
cx q[5],q[2];
u2(0,pi) q[2];
u1(pi/4) q[2];
u2(0,pi) q[5];
u3(5.589410446809829,0,0) q[5];
u3(0,0,pi/2) q[5];
u3(0,0,pi/4) q[7];
cx q[1],q[7];
cx q[1],q[8];
u3(0,0,-pi/4) q[7];
u3(pi/2,0,pi) q[7];
rz(-pi/4) q[8];
cx q[1],q[8];
u3(pi,0,pi) q[1];
h q[8];
h q[8];
cx q[1],q[8];
rz(-pi/4) q[8];
cx q[1],q[8];
u1(-pi/2) q[1];
u2(0,pi) q[1];
u1(-pi/2) q[1];
r_139920830762704(pi/2,0) q[1];
h q[8];
u(pi,pi/2,pi/2) q[8];
r_139920830763600(-pi/2,0) q[9];
u(2.9289757392371385,1.0855393074979447,-1.0855393074979447) q[9];
h q[9];
cx q[3],q[9];
rz(-pi/4) q[9];
cx q[3],q[9];
u3(pi,0,pi) q[3];
h q[9];
h q[9];
cx q[3],q[9];
rz(-pi/4) q[9];
cx q[3],q[9];
h q[3];
cx q[7],q[3];
rz(-pi/4) q[3];
cx q[7],q[3];
h q[3];
h q[3];
u3(pi,0,pi) q[7];
cx q[7],q[3];
rz(-pi/4) q[3];
cx q[7],q[3];
h q[3];
u3(0,0,pi/2) q[3];
cx q[3],q[0];
u1(3.8171564533553126) q[0];
cx q[3],q[0];
u2(0,pi) q[7];
cx q[4],q[7];
reset q[4];
u(0,0,pi/4) q[4];
u2(0,pi) q[7];
u2(0,pi) q[7];
cx q[8],q[7];
u1(-pi/4) q[7];
cx q[6],q[7];
u1(pi/4) q[7];
cx q[8],q[7];
u1(-pi/4) q[7];
cx q[6],q[7];
u1(pi/4) q[7];
u2(0,pi) q[7];
u2(0,pi) q[7];
cx q[3],q[7];
u1(-pi/4) q[7];
cx q[0],q[7];
u1(pi/4) q[7];
cx q[3],q[7];
u1(pi/4) q[3];
u1(-pi/4) q[7];
cx q[0],q[7];
cx q[0],q[3];
u1(pi/4) q[0];
u1(-pi/4) q[3];
cx q[0],q[3];
u3(pi/2,0,pi) q[3];
u3(0,0,pi/4) q[3];
u1(pi/4) q[7];
u2(0,pi) q[7];
u(4.471241824820643,3.278621899677415,2.614861988101286) q[7];
u1(pi/4) q[8];
cx q[6],q[8];
u1(pi/4) q[6];
u1(-pi/4) q[8];
cx q[6],q[8];
r_139920830948944(pi/2,0) q[6];
cx q[1],q[6];
u1(0.7167381230169633) q[6];
cx q[1],q[6];
r_139920830950352(-pi/2,0) q[1];
r_139920830950672(-pi/2,0) q[6];
u(pi,pi/2,pi/2) q[6];
cx q[6],q[7];
u1(2.9376462160094414) q[7];
cx q[6],q[7];
u(0,0,3.1359141554093766) q[7];
u(0,0,2.678480879396035) q[7];
u3(0,0,pi/2) q[8];
h q[9];
u(0,0,3.944674502950211) q[9];
u2(0,pi) q[9];
cx q[2],q[9];
u1(-pi/4) q[9];
cx q[2],q[9];
u3(0,0,3.1015495754427356) q[2];
cx q[2],q[5];
u3(0,0,-3.1015495754427356) q[5];
cx q[2],q[5];
cx q[4],q[2];
u(0,0,-pi/4) q[2];
cx q[4],q[2];
u(0,0,pi/4) q[2];
u3(0,0,3.1015495754427356) q[5];
cx q[5],q[3];
u3(0,0,-pi/4) q[3];
cx q[0],q[3];
u3(0,0,2.8488897172876855) q[0];
cx q[0],q[2];
u3(0,0,-2.8488897172876855) q[2];
cx q[0],q[2];
u(0,0,1.6195240066697587) q[0];
u3(0,0,2.8488897172876855) q[2];
u2(0,pi) q[2];
u3(0,0,pi/4) q[3];
cx q[5],q[3];
u3(0,0,-pi/4) q[3];
u3(pi/2,0,pi) q[3];
u(5.646933952724561,3.942476659297648,1.8698432262338225) q[3];
u(pi,pi/2,pi/2) q[3];
u3(pi/2,0,pi) q[5];
u3(0,0,pi/4) q[5];
cx q[7],q[0];
u(-2.9893973433292094,0,-2.678480879396035) q[0];
cx q[7],q[0];
u(2.9893973433292094,1.0589568727262761,0) q[0];
u1(pi/4) q[9];
u2(0,pi) q[9];
u3(0,0,-pi/4) q[9];
u3(pi/2,0,pi) q[9];
u3(0,0,pi/4) q[9];
cx q[8],q[9];
u3(0,0,-pi/4) q[9];
cx q[1],q[9];
cx q[1],q[5];
u3(0,0,-pi/4) q[5];
u3(0,0,pi/4) q[9];
cx q[8],q[9];
u3(0,0,2.5822280228300025) q[8];
cx q[8],q[4];
u3(0,0,-2.5822280228300025) q[4];
cx q[8],q[4];
u3(0,0,2.5822280228300025) q[4];
cx q[6],q[4];
h q[6];
cx q[4],q[6];
tdg q[6];
cx q[8],q[6];
t q[6];
cx q[4],q[6];
t q[4];
tdg q[6];
cx q[8],q[6];
t q[6];
h q[6];
cx q[8],q[4];
tdg q[4];
t q[8];
cx q[8],q[4];
cx q[6],q[4];
u3(0,0,-pi/4) q[9];
u3(pi/2,0,pi) q[9];
cx q[9],q[5];
u3(0,0,pi/4) q[5];
cx q[1],q[5];
u1(-pi/2) q[1];
u2(0,pi) q[1];
u1(-pi/2) q[1];
u3(0,0,-pi/4) q[5];
u3(pi/2,0,pi) q[5];
u3(0,0,pi/2) q[5];
cx q[9],q[2];
u1(5.754503949756053) q[2];
cx q[9],q[2];
u2(0,pi) q[2];
