OPENQASM 2.0;
include "qelib1.inc";
gate r(param0,param1) q0 { u3(2.6368701044265,0,0) q0; }
gate r_139920839411536(param0,param1) q0 { u3(1.541203932672964,0,0) q0; }
gate r_139920833816976(param0,param1) q0 { u3(1.6428199808612811,0,0) q0; }
gate r_139920833822160(param0,param1) q0 { u3(-1.6428199808612811,0,0) q0; }
gate r_139921721085264(param0,param1) q0 { u3(-1.541203932672964,0,0) q0; }
gate r_139920835001680(param0,param1) q0 { u3(-2.6368701044265,0,0) q0; }
gate r_139921728696720(param0,param1) q0 { u3(-1.2117147827609498,0,0) q0; }
gate r_139921728698768(param0,param1) q0 { u3(-1.2117147827609498,0,0) q0; }
gate r_139920830871824(param0,param1) q0 { u3(1.2793844095693925,0,0) q0; }
gate r_139920830863184(param0,param1) q0 { u3(-1.2793844095693925,0,0) q0; }
gate r_139920839815184(param0,param1) q0 { u3(0.8758936464059278,0,0) q0; }
gate r_139920839813136(param0,param1) q0 { u3(-0.8758936464059278,0,0) q0; }
gate r_139921728839056(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920838515920(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920838517072(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920838515792(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920831374288(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920839820752(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920839830736(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920839833936(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920839826576(param0,param1) q0 { u3(2.727691452284502,0,0) q0; }
gate r_139920839824656(param0,param1) q0 { u3(-2.727691452284502,0,0) q0; }
gate r_139920839828240(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920837161296(param0,param1) q0 { u3(pi/2,-pi/2,pi/2) q0; }
gate r_139920834545424(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920834548688(param0,param1) q0 { u3(-pi/2,-pi/2,pi/2) q0; }
gate r_139920834553424(param0,param1) q0 { u3(1.6150779065906702,0,0) q0; }
gate r_139920834556048(param0,param1) q0 { u3(-1.6150779065906702,0,0) q0; }
qreg q[10];
u3(0,0,pi/4) q[1];
u(1.7027678297869249,3.0312647977688143,2.9578468104973505) q[2];
u(pi,0,pi) q[2];
u3(0,0,2.5957795031711997) q[2];
u3(pi/2,0,pi) q[3];
u3(pi/2,0,pi) q[4];
cx q[0],q[6];
cx q[6],q[0];
cx q[0],q[6];
u1(-pi/2) q[0];
cx q[1],q[0];
u1(pi/2) q[0];
r(2.6368701044265,pi/2) q[0];
u3(0,0,-pi/4) q[7];
u1(pi/2) q[7];
u2(0,pi) q[7];
u1(pi/4) q[7];
cx q[3],q[7];
u1(-pi/4) q[7];
u2(0,pi) q[7];
u1(-pi/2) q[7];
cx q[1],q[7];
u(pi/2,1.5708734302418408,0.12815181894927313) q[1];
u1(1.0820325535127813) q[1];
u1(pi/2) q[1];
u1(pi/2) q[7];
u2(0,pi) q[7];
u1(pi/2) q[7];
u3(0,0,pi/2) q[8];
cx q[5],q[8];
u(-1.2779261732535188,0,0) q[8];
cx q[5],q[8];
u(0.9119293054500426,-0.2222354277609333,0.2222354277609333) q[5];
u(0,0,1.344263001305276) q[5];
r_139920839411536(1.541203932672964,pi/2) q[5];
u(1.2779261732535188,-pi/2,0) q[8];
cx q[6],q[8];
u1(0.8462240507581523) q[8];
cx q[6],q[8];
u3(0,0,-pi/2) q[6];
u1(pi/4) q[6];
r_139920833816976(1.6428199808612811,pi/2) q[8];
cx q[3],q[8];
r_139920833822160(-1.6428199808612811,pi/2) q[8];
cx q[3],q[8];
u1(pi/2) q[3];
u2(0,pi) q[3];
u1(pi/2) q[3];
id q[3];
u3(0,0,pi/4) q[9];
u2(0,pi) q[9];
cx q[4],q[9];
cx q[2],q[4];
u3(0,0,-2.5957795031711997) q[4];
cx q[2],q[4];
u3(0.29834143876365793,-pi/2,pi/2) q[2];
u(pi/2,6.03394322454225,0.475308140215157) q[2];
u3(0,0,2.5957795031711997) q[4];
cx q[4],q[5];
r_139921721085264(-1.541203932672964,pi/2) q[5];
cx q[4],q[5];
cx q[8],q[5];
u1(0.06266439634368064) q[5];
cx q[8],q[5];
cx q[3],q[8];
u1(pi/2) q[5];
u2(0,pi) q[5];
u1(pi/4) q[5];
cx q[8],q[3];
cx q[3],q[8];
u2(0,pi) q[9];
cx q[9],q[0];
r_139920835001680(-2.6368701044265,pi/2) q[0];
cx q[9],q[0];
cx q[0],q[7];
u1(2.7698180961355874) q[7];
cx q[0],q[7];
u2(0,pi) q[0];
u3(0,0,pi/2) q[7];
u1(-1.6910237955052827) q[7];
u1(pi/2) q[7];
u2(0,pi) q[9];
cx q[6],q[9];
u1(-pi/4) q[9];
cx q[6],q[9];
u(0,0,0.8827324613923432) q[6];
cx q[6],q[4];
u(0,0,-0.8827324613923432) q[4];
cx q[6],q[4];
u(0,0,0.8827324613923432) q[4];
u1(-pi/2) q[4];
sdg q[4];
h q[4];
sdg q[4];
u1(pi/2) q[4];
cx q[4],q[1];
r_139921728696720(-1.2117147827609498,pi/2) q[1];
r_139921728698768(-1.2117147827609498,pi/2) q[4];
cx q[4],q[1];
u1(-pi/2) q[1];
u1(-1.0820325535127813) q[1];
u3(pi/2,0,pi) q[1];
u3(0,0,pi/4) q[1];
cx q[2],q[1];
u3(0,0,-pi/4) q[1];
cx q[3],q[1];
u3(0,0,pi/4) q[1];
cx q[2],q[1];
u3(0,0,-pi/4) q[1];
u3(pi/2,0,pi) q[1];
u(pi,0,pi) q[2];
u3(0,0,-pi/4) q[3];
u1(pi/2) q[3];
u2(0,pi) q[3];
u1(pi/2) q[3];
u(0,0,pi/4) q[3];
u1(-pi/2) q[4];
s q[4];
h q[4];
s q[4];
u1(pi/2) q[4];
u1(pi/2) q[4];
u2(0,pi) q[4];
u1(pi/4) q[6];
cx q[6],q[0];
u1(-pi/4) q[0];
cx q[6],q[0];
u1(pi/4) q[0];
u2(0,pi) q[0];
u1(pi/2) q[6];
cx q[4],q[6];
cx q[6],q[4];
u2(0,pi) q[4];
u2(0,pi) q[6];
u1(pi/4) q[9];
u2(0,pi) q[9];
u3(0,0,pi/2) q[9];
cx q[9],q[5];
u1(-pi/4) q[5];
u2(0,pi) q[5];
u1(-pi/2) q[5];
u1(-pi/2) q[5];
sdg q[5];
h q[5];
sdg q[5];
u1(pi/2) q[5];
cx q[5],q[7];
r_139920830871824(1.2793844095693925,pi/2) q[5];
r_139920830863184(-1.2793844095693925,pi/2) q[7];
cx q[5],q[7];
u1(-pi/2) q[5];
s q[5];
h q[5];
s q[5];
u1(pi/2) q[5];
u3(pi/2,0,pi) q[5];
u3(0,0,pi/4) q[5];
cx q[1],q[5];
u3(0,0,-pi/4) q[5];
u1(-pi/2) q[7];
u1(1.6910237955052827) q[7];
u2(0,pi) q[9];
cx q[0],q[9];
u1(-pi/4) q[9];
cx q[8],q[9];
u1(pi/4) q[9];
cx q[0],q[9];
u1(pi/4) q[0];
u1(-pi/4) q[9];
cx q[8],q[9];
cx q[8],q[0];
u1(-pi/4) q[0];
u1(pi/4) q[8];
cx q[8],q[0];
u(0,0,2.8547124689095127) q[0];
cx q[0],q[6];
u(0,0,-2.8547124689095127) q[6];
cx q[0],q[6];
r_139920839815184(0.8758936464059278,pi/2) q[0];
cx q[2],q[0];
r_139920839813136(-0.8758936464059278,pi/2) q[0];
cx q[2],q[0];
u(3.163557591054361,1.5813738674224895,4.183959095733899) q[0];
id q[0];
u3(0.560951796500419,-pi/2,pi/2) q[2];
u(0,0,2.8547124689095127) q[6];
u2(0,pi) q[6];
cx q[8],q[5];
u3(0,0,pi/4) q[5];
cx q[1],q[5];
u(0,0,4.472131998490641) q[1];
u3(0,0,pi/2) q[1];
u3(0,0,-pi/4) q[5];
u3(pi/2,0,pi) q[5];
u(0,0,pi/4) q[8];
cx q[8],q[5];
u(0,0,-pi/4) q[5];
cx q[8],q[5];
u(0,0,pi/4) q[5];
cx q[5],q[1];
u(-2.154620717886341,0,0) q[1];
cx q[5],q[1];
u(2.154620717886341,-pi/2,0) q[1];
u3(0,0,-pi/2) q[1];
u(0,0,-0.5777613118470906) q[1];
u3(0,0,1.1947397098635095) q[8];
u1(pi/4) q[9];
u2(0,pi) q[9];
cx q[9],q[4];
u1(-pi/4) q[4];
cx q[7],q[4];
u1(pi/4) q[4];
cx q[9],q[4];
u1(-pi/4) q[4];
cx q[7],q[4];
u1(pi/4) q[4];
u2(0,pi) q[4];
u(0,0,-2.2820402235718382) q[4];
u1(pi/4) q[9];
cx q[7],q[9];
u1(pi/4) q[7];
u1(-pi/4) q[9];
cx q[7],q[9];
u(0,0,5.3105378360967554) q[7];
u(0,0,2.770096216230075) q[7];
cx q[7],q[4];
u(-0.47916458216562785,0,-2.770096216230075) q[4];
cx q[7],q[4];
u(0.47916458216562785,5.052136439801913,0) q[4];
u(0,0,0.5346146299613418) q[4];
u1(-pi/2) q[4];
u2(0,pi) q[4];
u1(-pi/2) q[4];
u(0,0,2.5648939953655034) q[4];
r_139921728839056(pi/2,0) q[4];
u3(0,0,0.5889538931806698) q[7];
cx q[8],q[7];
u(-2.6676080823716424,0,-1.1947397098635095) q[7];
cx q[8],q[7];
u(2.6676080823716424,0.6057858166828396,0) q[7];
u1(pi/2) q[7];
u2(0,pi) q[7];
u1(pi/4) q[7];
cx q[8],q[7];
u1(-pi/4) q[7];
u2(0,pi) q[7];
u1(-pi/2) q[7];
u3(pi/2,0,pi) q[7];
u3(0,0,pi/4) q[7];
cx q[0],q[7];
u3(0,0,-pi/4) q[7];
u2(0,pi) q[9];
cx q[9],q[6];
u1(5.154241616888874) q[6];
cx q[9],q[6];
u2(0,pi) q[6];
cx q[3],q[6];
u(0,0,-pi/4) q[6];
cx q[3],q[6];
cx q[2],q[3];
u2(0,pi) q[3];
h q[3];
u(0,0,pi/4) q[6];
u(0,0,pi/4) q[6];
cx q[6],q[5];
u(0,0,-pi/4) q[5];
cx q[6],q[5];
u(0,0,pi/4) q[5];
cx q[5],q[7];
u3(pi/2,0,pi) q[6];
r_139920838515920(pi/2,0) q[6];
cx q[4],q[6];
u1(2.4721828647038806) q[6];
cx q[4],q[6];
r_139920838517072(-pi/2,0) q[4];
r_139920838515792(-pi/2,0) q[6];
u3(0,0,pi/2) q[6];
u3(0,0,pi/4) q[7];
cx q[0],q[7];
u3(0,0,-pi/4) q[7];
u3(pi/2,0,pi) q[7];
u3(0,0,pi/4) q[7];
u1(0.5531906090317026) q[7];
cx q[4],q[7];
u1(-0.5531906090317026) q[7];
cx q[4],q[7];
u3(0,0,2.0169537253021015) q[4];
cx q[8],q[3];
tdg q[3];
cx q[2],q[3];
t q[3];
cx q[8],q[3];
tdg q[3];
cx q[2],q[3];
t q[3];
h q[3];
u2(0,pi) q[3];
t q[8];
cx q[2],q[8];
t q[2];
tdg q[8];
cx q[2],q[8];
u1(-pi/2) q[2];
cx q[3],q[2];
u1(pi/2) q[2];
u3(3.0137294184151533,-pi/2,pi/2) q[2];
u(0.48453160399017847,2.821772151125831,-2.821772151125831) q[2];
u3(0,0,0.19202948554768406) q[2];
cx q[3],q[6];
u(-2.390857582694593,0,0) q[6];
cx q[3],q[6];
u(5.762079986193337,2.827849195108253,3.4364818764324694) q[3];
u(2.390857582694593,-pi/2,0) q[6];
u3(pi/2,0,pi) q[6];
u3(0,0,pi/4) q[6];
u3(pi/2,0,pi) q[8];
u3(0,0,-pi/4) q[8];
u2(0,pi) q[9];
u(pi,pi/2,pi/2) q[9];
u(0,0,2.0700122330848068) q[9];
u(0,0,5.853907455791642) q[9];
u(0,0,3.8317789295054925) q[9];
cx q[9],q[1];
u(-0.7805826096003631,0,-3.8317789295054925) q[1];
cx q[9],q[1];
u(0.7805826096003631,4.409540241352583,0) q[1];
u3(pi/2,0,pi) q[1];
u3(0,0,pi/4) q[1];
cx q[0],q[1];
u3(0,0,-pi/4) q[1];
cx q[5],q[1];
u3(0,0,pi/4) q[1];
cx q[0],q[1];
u3(0,0,-pi/4) q[1];
u3(pi/2,0,pi) q[1];
reset q[1];
u1(1.903978253620976) q[1];
h q[5];
cx q[0],q[5];
rz(-pi/4) q[5];
cx q[0],q[5];
u3(pi,0,pi) q[0];
h q[5];
h q[5];
cx q[0],q[5];
rz(-pi/4) q[5];
cx q[0],q[5];
cx q[0],q[6];
h q[5];
cx q[4],q[5];
u3(0,0,-2.0169537253021015) q[5];
cx q[4],q[5];
u3(0,0,2.0169537253021015) q[5];
u(0,0,pi/4) q[5];
cx q[5],q[4];
u(0,0,-pi/4) q[4];
cx q[5],q[4];
u(0,0,pi/4) q[4];
u3(0,0,-pi/4) q[6];
u3(pi/2,0,pi) q[6];
cx q[7],q[6];
u3(0,0,pi/4) q[6];
cx q[8],q[6];
u3(0,0,-pi/4) q[6];
cx q[7],q[6];
cx q[3],q[7];
h q[3];
u3(0,0,pi/4) q[6];
cx q[7],q[3];
tdg q[3];
cx q[8],q[6];
u3(0,0,-pi/4) q[6];
u3(pi/2,0,pi) q[6];
u3(0,0,pi/4) q[6];
cx q[0],q[6];
u3(0,0,4.944432224171804) q[0];
cx q[0],q[2];
u(-2.454641271819731,0,-4.944432224171804) q[2];
cx q[0],q[2];
u(2.454641271819731,4.752402738624119,0) q[2];
u3(pi/2,0,pi) q[2];
u3(0,0,pi/4) q[2];
cx q[0],q[2];
u3(0,0,-pi/4) q[2];
cx q[4],q[2];
u3(0,0,pi/4) q[2];
cx q[0],q[2];
u3(0,0,-pi/4) q[2];
u3(pi/2,0,pi) q[2];
u(pi,0,pi) q[2];
u1(pi/2) q[2];
u2(0,pi) q[2];
u(0,0,5.162775900617509) q[4];
u(0,0,3.174462963521596) q[4];
u3(0,0,-pi/4) q[6];
u3(pi/2,0,pi) q[6];
u3(0,0,2.5424225052892533) q[6];
cx q[8],q[3];
t q[3];
cx q[7],q[3];
tdg q[3];
t q[7];
cx q[8],q[3];
t q[3];
h q[3];
cx q[8],q[7];
tdg q[7];
t q[8];
cx q[8],q[7];
cx q[3],q[7];
u(0,0,3.5748665612967163) q[3];
u1(2.456426962242339) q[7];
cx q[5],q[7];
u1(-2.456426962242339) q[7];
cx q[5],q[7];
u3(1.258097795644986,0,0) q[5];
u2(0,pi) q[5];
u3(0.11122255405755092,0,0) q[7];
u(4.720406707139022,2.5287878051985406,2.701517667147926) q[7];
u2(0,pi) q[8];
u3(0,0,-pi/4) q[9];
u3(0,0,pi/4) q[9];
cx q[9],q[1];
u1(-1.903978253620976) q[1];
cx q[9],q[1];
cx q[6],q[1];
u3(0,0,-2.5424225052892533) q[1];
cx q[6],q[1];
u3(0,0,2.5424225052892533) q[1];
u2(0,pi) q[1];
u3(6.133557669359828,-pi/2,pi/2) q[6];
u1(pi/2) q[6];
u2(0,pi) q[6];
u1(pi/2) q[6];
u2(0,pi) q[6];
cx q[5],q[6];
u1(0.6814724449073827) q[6];
cx q[5],q[6];
u2(0,pi) q[5];
u3(5.676405684752871,-pi/2,pi/2) q[5];
u2(0,pi) q[6];
cx q[6],q[7];
cx q[7],q[6];
cx q[6],q[7];
u1(pi/2) q[6];
cx q[8],q[1];
u1(5.944758694743201) q[1];
cx q[8],q[1];
u2(0,pi) q[1];
cx q[0],q[1];
h q[0];
cx q[1],q[0];
tdg q[0];
cx q[3],q[0];
t q[0];
cx q[1],q[0];
tdg q[0];
t q[1];
cx q[3],q[0];
t q[0];
h q[0];
cx q[3],q[1];
tdg q[1];
t q[3];
cx q[3],q[1];
cx q[0],q[1];
u1(-pi/2) q[1];
u2(0,pi) q[1];
u1(-pi/2) q[1];
u(0,0,pi/4) q[3];
cx q[3],q[0];
u(0,0,-pi/4) q[0];
cx q[3],q[0];
u(0,0,pi/4) q[0];
u3(0,0,pi/2) q[0];
u1(1.6604890298202708) q[0];
cx q[5],q[0];
u1(-1.6604890298202708) q[0];
cx q[5],q[0];
id q[5];
u2(0,pi) q[8];
id q[8];
u1(pi/2) q[8];
cx q[2],q[8];
cx q[8],q[2];
u(5.890581819045502,3.8279714262202424,1.9210343015469808) q[2];
r_139920831374288(pi/2,0) q[2];
u2(0,pi) q[8];
u2(0,pi) q[8];
cx q[3],q[8];
u1(-pi/4) q[8];
cx q[1],q[8];
u1(pi/4) q[8];
cx q[3],q[8];
u1(pi/4) q[3];
u1(-pi/4) q[8];
cx q[1],q[8];
cx q[1],q[3];
u1(pi/4) q[1];
u1(-pi/4) q[3];
cx q[1],q[3];
u(0,0,-pi/4) q[1];
cx q[1],q[7];
reset q[3];
u3(0,0,1.303281356241339) q[3];
u(0,0,pi/4) q[7];
cx q[1],q[7];
u3(0,0,pi/4) q[1];
u3(pi/2,0,pi) q[1];
u3(0,0,pi/4) q[1];
u(0,0,-pi/4) q[7];
u2(0,pi) q[7];
u1(pi/4) q[8];
u2(0,pi) q[8];
r_139920839820752(pi/2,0) q[8];
cx q[2],q[8];
u1(0.2626484344488051) q[8];
cx q[2],q[8];
r_139920839830736(-pi/2,0) q[2];
u1(pi/2) q[2];
u2(0,pi) q[2];
u1(pi/4) q[2];
r_139920839833936(-pi/2,0) q[8];
u2(0,pi) q[8];
cx q[7],q[8];
u1(1.4752921497158213) q[8];
cx q[7],q[8];
u2(0,pi) q[7];
u2(0,pi) q[7];
u2(0,pi) q[8];
r_139920839826576(2.727691452284502,pi/2) q[8];
cx q[5],q[8];
r_139920839824656(-2.727691452284502,pi/2) q[8];
cx q[5],q[8];
r_139920839828240(pi/2,0) q[5];
u(pi,pi/2,pi/2) q[9];
u(pi,0,pi) q[9];
u(0,0,-2.173073304193525) q[9];
cx q[4],q[9];
u(-3.034238110125209,0,-3.174462963521596) q[9];
cx q[4],q[9];
u(3.034238110125209,5.347536267715121,0) q[9];
u(0,0,pi/4) q[9];
cx q[9],q[4];
u(0,0,-pi/4) q[4];
cx q[9],q[4];
u(0,0,pi/4) q[4];
u(0,0,4.795972098680909) q[4];
u1(pi/2) q[4];
u2(0,pi) q[4];
cx q[4],q[6];
cx q[6],q[4];
cx q[0],q[4];
cx q[4],q[0];
cx q[0],q[4];
u(pi,0,pi) q[0];
u2(0,pi) q[6];
cx q[3],q[6];
u3(0,0,-1.303281356241339) q[6];
cx q[3],q[6];
cx q[3],q[7];
u3(0,0,1.303281356241339) q[6];
cx q[6],q[1];
u3(0,0,-pi/4) q[1];
u3(pi/2,0,pi) q[1];
u1(-pi/4) q[7];
reset q[9];
u1(pi/2) q[9];
u2(0,pi) q[9];
u1(pi/2) q[9];
cx q[9],q[2];
u1(-pi/4) q[2];
u2(0,pi) q[2];
u1(-pi/2) q[2];
cx q[2],q[1];
u3(0,0,pi/4) q[1];
cx q[4],q[1];
u3(0,0,-pi/4) q[1];
cx q[2],q[1];
u3(0,0,pi/4) q[1];
reset q[2];
cx q[4],q[1];
u3(0,0,-pi/4) q[1];
u3(pi/2,0,pi) q[1];
u3(0,0,pi/4) q[1];
u2(0,pi) q[4];
cx q[6],q[1];
u3(0,0,-pi/4) q[1];
u3(pi/2,0,pi) q[1];
r_139920837161296(pi/2,0) q[1];
cx q[1],q[5];
u1(5.775336548251072) q[5];
cx q[1],q[5];
r_139920834545424(-pi/2,0) q[1];
u1(pi/2) q[1];
u2(0,pi) q[1];
u1(pi/2) q[1];
r_139920834548688(-pi/2,0) q[5];
r_139920834553424(1.6150779065906702,pi/2) q[5];
u(0,0,2.4479957595616786) q[6];
cx q[9],q[7];
u1(pi/4) q[7];
cx q[3],q[7];
u1(pi/4) q[3];
u1(-pi/4) q[7];
cx q[9],q[7];
u1(pi/4) q[7];
u2(0,pi) q[7];
u1(-pi/2) q[7];
cx q[8],q[7];
u1(pi/2) q[7];
cx q[6],q[7];
h q[6];
cx q[7],q[6];
tdg q[6];
cx q[2],q[6];
t q[6];
cx q[7],q[6];
tdg q[6];
cx q[2],q[6];
t q[6];
h q[6];
t q[7];
cx q[2],q[7];
t q[2];
tdg q[7];
cx q[2],q[7];
cx q[6],q[7];
u3(0,0,pi/4) q[8];
cx q[9],q[3];
u1(-pi/4) q[3];
u1(pi/4) q[9];
cx q[9],q[3];
cx q[0],q[3];
u1(2.793352110078187) q[3];
cx q[0],q[3];
cx q[0],q[5];
r_139920834556048(-1.6150779065906702,pi/2) q[5];
cx q[0],q[5];
u2(0,pi) q[9];
cx q[9],q[4];
u1(3.9344399186874663) q[4];
cx q[9],q[4];
u2(0,pi) q[4];
u(0,0,2.146644153547165) q[4];
u2(0,pi) q[9];
cx q[3],q[9];
