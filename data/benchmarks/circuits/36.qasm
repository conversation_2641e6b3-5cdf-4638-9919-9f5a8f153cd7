OPENQASM 2.0;
include "qelib1.inc";
qreg q[10];
cx q[0],q[1];
u3(pi/2,0,pi) q[0];
u2(0,pi) q[0];
u(1.2980561686270282,0,0) q[2];
cx q[2],q[0];
u1(-pi/4) q[0];
reset q[0];
u(1.0637276319926654,0.8693851630569345,-0.8693851630569345) q[3];
u(0,0,-pi/4) q[3];
u3(0,0,pi/4) q[4];
u3(pi/2,0,pi) q[5];
u3(pi/2,0,pi) q[6];
cx q[4],q[6];
u3(0,0,-pi/4) q[6];
cx q[4],q[6];
cx q[4],q[0];
u1(pi/4) q[0];
cx q[2],q[0];
u1(-pi/4) q[0];
u1(pi/4) q[2];
cx q[4],q[0];
u1(pi/4) q[0];
u2(0,pi) q[0];
u3(pi/2,0,pi) q[0];
u(5.983179464729261,4.612630640136753,4.334449400686934) q[0];
u(0,0,pi) q[0];
u3(0,0,-pi/2) q[0];
u3(pi/2,0,pi) q[0];
u3(0,0,-pi/2) q[0];
cx q[4],q[2];
u1(-pi/4) q[2];
u1(pi/4) q[4];
cx q[4],q[2];
u(0,0,2.173820523812208) q[2];
u(0,0,0.847397078581782) q[4];
reset q[4];
u3(0,0,pi/4) q[6];
u3(pi/2,0,pi) q[6];
u(0,0,0.6198974248695438) q[6];
cx q[6],q[1];
u(0,0,-0.6198974248695438) q[1];
cx q[6],q[1];
u(0,0,0.6198974248695438) q[1];
cx q[6],q[3];
u(0,0,-pi/4) q[6];
u(pi/2,0,pi) q[6];
u(0,0,pi/4) q[6];
cx q[7],q[5];
u3(0,0,-pi/4) q[5];
u(0,0,-pi/2) q[8];
id q[8];
cx q[8],q[1];
u3(0,0,1.4075750386544732) q[1];
cx q[8],q[1];
u(0,0,pi/4) q[1];
cx q[1],q[4];
u(0,0,-pi/4) q[4];
cx q[1],q[4];
u3(2.9700172705424306,0,0) q[1];
u(0,0,pi/4) q[4];
u(pi/2,0,pi) q[4];
u3(pi/2,0,pi) q[8];
cx q[9],q[5];
u3(0,0,pi/4) q[5];
cx q[7],q[5];
u3(0,0,-pi/4) q[5];
u3(0,0,pi/4) q[7];
cx q[9],q[5];
u3(0,0,pi/4) q[5];
u3(pi/2,0,pi) q[5];
u(1.3011685613486907,0,0) q[5];
u3(0,0,pi/2) q[5];
u3(pi/2,0,pi) q[5];
u3(0,0,pi/2) q[5];
u3(0,0,-pi/2) q[5];
u3(pi/2,0,pi) q[5];
u3(0,0,-pi/2) q[5];
cx q[9],q[7];
u3(0,0,-pi/4) q[7];
u3(0,0,pi/4) q[9];
cx q[9],q[7];
u2(0,pi) q[7];
cx q[9],q[7];
u1(-pi/4) q[7];
cx q[9],q[7];
u2(0,pi) q[7];
u2(0,pi) q[7];
u(pi,0,pi) q[9];
cx q[9],q[7];
u1(-pi/4) q[7];
cx q[9],q[7];
u2(0,pi) q[7];
cx q[2],q[7];
u(0,0,-2.173820523812208) q[7];
cx q[2],q[7];
u3(0,0,pi/4) q[2];
cx q[2],q[8];
u(0,0,2.173820523812208) q[7];
u3(0,0,-pi/2) q[7];
u3(pi/2,0,pi) q[7];
u3(0,0,-pi/2) q[7];
u3(0,0,-pi/4) q[8];
cx q[2],q[8];
cx q[2],q[6];
u(0,0,-pi/4) q[6];
cx q[7],q[6];
u(0,0,pi/4) q[6];
cx q[2],q[6];
u(0,0,-pi/4) q[6];
u(pi/2,0,pi) q[6];
u3(pi/2,0,pi) q[7];
reset q[8];
u3(0,0,pi/4) q[8];
u3(pi/2,0,pi) q[8];
id q[8];
u3(0,0,2.7246116368547018) q[8];
cx q[2],q[8];
u3(0,0,-2.7246116368547018) q[8];
cx q[2],q[8];
u3(0,0,pi/2) q[2];
u3(pi/2,0,pi) q[2];
u3(0,0,pi/2) q[2];
u(0,0,3.6746459925910533) q[9];
u(0,0,-pi/4) q[9];
cx q[9],q[3];
u(0,0,pi/4) q[3];
cx q[9],q[3];
u(0,0,-pi/4) q[3];
cx q[3],q[1];
u3(-2.9700172705424306,0,0) q[1];
cx q[3],q[1];
cx q[1],q[7];
u(0,0,pi/2) q[1];
u(0,0,0.046360794054885274) q[3];
u(0,0,3.462484911124366) q[3];
u3(pi/2,0,pi) q[7];
u(0,0,pi) q[7];
u3(0,0,-pi/2) q[9];
cx q[5],q[9];
cx q[0],q[5];
u2(0,pi) q[0];
cx q[5],q[0];
u1(-pi/4) q[0];
cx q[6],q[0];
u1(pi/4) q[0];
cx q[5],q[0];
u1(-pi/4) q[0];
u1(pi/4) q[5];
cx q[6],q[0];
u1(pi/4) q[0];
u2(0,pi) q[0];
cx q[6],q[5];
u1(-pi/4) q[5];
u1(pi/4) q[6];
cx q[6],q[5];
cx q[0],q[5];
u(3.7529557834994987,0,0) q[0];
u3(0,0,-pi/2) q[0];
u(0,0,3.6674814457545875) q[5];
u(5.590042856686649,2.2892005864637865,4.4933283118526175) q[5];
cx q[6],q[1];
u(-1.4359293467334537,0,0) q[1];
cx q[6],q[1];
u(1.4359293467334537,-pi/2,0) q[1];
cx q[7],q[0];
u3(0,0,pi/2) q[0];
u(0,0,1.83430039056858) q[0];
cx q[0],q[5];
u(0,0,-1.83430039056858) q[5];
cx q[0],q[5];
u(0,0,1.83430039056858) q[5];
u(0,0,pi/2) q[5];
u(0,0,pi/4) q[5];
u3(0,0,6.2751021927880934) q[5];
u3(0,0,pi/2) q[5];
u(1.8034862732155148,2.8635012288935853,5.6111078959471445) q[7];
u(0,0,-pi/4) q[7];
u3(0,0,pi/4) q[7];
u3(0,0,pi/2) q[9];
cx q[9],q[4];
cx q[4],q[9];
u(0,0,pi/4) q[4];
cx q[4],q[8];
u(0,0,-pi/4) q[8];
cx q[4],q[8];
u(pi/2,3.917143658844258,5.258563051643269) q[4];
u(0,0,pi/4) q[8];
u3(pi/2,0,pi) q[8];
u2(0,pi) q[8];
cx q[1],q[8];
u1(-pi/4) q[8];
cx q[2],q[8];
u1(pi/4) q[8];
cx q[1],q[8];
u1(pi/4) q[1];
u1(-pi/4) q[8];
cx q[2],q[8];
cx q[2],q[1];
u1(-pi/4) q[1];
u1(pi/4) q[2];
cx q[2],q[1];
cx q[2],q[1];
cx q[1],q[2];
u(0,0,1.1316038585308634) q[1];
u3(0,0,pi/2) q[1];
u3(pi/2,0,pi) q[1];
u3(0,0,pi/2) q[1];
u3(0,0,pi/4) q[2];
u1(pi/4) q[8];
u2(0,pi) q[8];
u3(pi/2,0,pi) q[8];
u(pi/2,0,pi) q[8];
u(0,0,pi/4) q[8];
cx q[4],q[8];
u(0,0,-pi/4) q[8];
u(0,0,-1.01488265341452) q[9];
cx q[3],q[9];
u(-0.33815803169777053,0,-3.462484911124366) q[9];
cx q[3],q[9];
u3(pi/2,0,pi) q[3];
cx q[6],q[3];
u3(0,0,3.649734051949084) q[3];
cx q[6],q[3];
u3(pi/2,0,pi) q[3];
u3(0,0,-pi/2) q[3];
u3(pi/2,0,pi) q[3];
u3(0,0,-pi/2) q[3];
u(pi/2,0,pi) q[6];
u3(pi/2,0,pi) q[6];
cx q[0],q[6];
u3(pi/2,0,pi) q[0];
u3(pi/2,0,pi) q[6];
u(pi/2,4.457188506773677,5.75962394760025) q[6];
cx q[7],q[0];
u3(0,0,-pi/4) q[0];
cx q[7],q[0];
u3(0,0,pi/4) q[0];
u3(pi/2,0,pi) q[0];
cx q[1],q[0];
id q[7];
u(1.747563760322998,0,0) q[7];
u(0,0,pi/2) q[7];
u(0,0,-0.32060621227465236) q[7];
u(0.33815803169777053,4.477367564538886,0) q[9];
u(0,0,0.17117663321847956) q[9];
cx q[9],q[8];
u(0,0,pi/4) q[8];
cx q[4],q[8];
u(0,0,-pi/4) q[8];
u(pi/2,0,pi) q[8];
u3(pi/2,0,pi) q[8];
cx q[4],q[8];
u3(0,0,-pi/4) q[8];
cx q[3],q[8];
u3(0,0,pi/4) q[8];
cx q[4],q[8];
u3(0,0,pi/4) q[4];
u3(0,0,-pi/4) q[8];
cx q[3],q[8];
cx q[3],q[4];
u3(0,0,pi/4) q[3];
u3(0,0,-pi/4) q[4];
cx q[3],q[4];
u(0,0,-pi/2) q[3];
u3(0,0,3.102544273159943) q[3];
u3(0,0,pi/4) q[8];
u3(pi/2,0,pi) q[8];
u(0,0,2.2462708114744103) q[8];
u(0,0,-pi/4) q[8];
u(0,0,pi/4) q[8];
cx q[8],q[1];
u(0,0,-pi/4) q[1];
cx q[8],q[1];
u(0,0,pi/4) q[1];
u(0,0,-pi/2) q[1];
u(0,0,0.6354535999992976) q[1];
u(0,0,1.1625346826479657) q[1];
cx q[1],q[7];
u(-0.6954441204001935,0,-1.1625346826479657) q[7];
cx q[1],q[7];
u(0,0,0.4510516573189698) q[1];
u(0,0,5.058084904729555) q[1];
u(0.6954441204001935,1.483140894922618,0) q[7];
u3(0,0,0.15989601915742077) q[7];
u3(pi/2,0,pi) q[9];
cx q[2],q[9];
u3(0,0,-pi/4) q[9];
cx q[2],q[9];
u3(pi/2,0,pi) q[2];
cx q[4],q[2];
u3(pi/2,0,pi) q[2];
cx q[2],q[3];
u3(0,0,-3.102544273159943) q[3];
cx q[2],q[3];
u(0,0,pi/2) q[2];
cx q[0],q[2];
u(-1.0172386149143147,0,0) q[2];
cx q[0],q[2];
u3(pi/2,0,pi) q[0];
u(1.0172386149143147,-pi/2,0) q[2];
u3(0,0,pi/2) q[2];
u3(pi/2,0,pi) q[2];
u3(0,0,pi/4) q[2];
u(0,0,1.4991671237702002) q[3];
u(0,0,pi/2) q[3];
u(0,0,-pi/4) q[3];
cx q[3],q[7];
u(0,0,-pi/4) q[4];
u(pi/2,0,pi) q[4];
cx q[4],q[2];
u3(0,0,-pi/4) q[2];
u3(pi/2,0,pi) q[2];
u3(0,0,-pi/2) q[2];
u(4.681805625625833,0,0) q[2];
u3(pi/2,0,pi) q[4];
u3(0,0,-0.15989601915742077) q[7];
cx q[3],q[7];
u(0,0,-pi/4) q[3];
id q[3];
cx q[8],q[0];
u3(pi/2,0,pi) q[0];
u(pi/2,0,pi) q[0];
u3(pi/2,0,pi) q[0];
u2(0,pi) q[0];
u(0,0,pi/2) q[8];
cx q[8],q[0];
u1(-pi/4) q[0];
u3(0,0,pi/4) q[9];
u3(pi/2,0,pi) q[9];
u(0,0,-pi/2) q[9];
u3(0,0,1.1751852380466716) q[9];
cx q[6],q[9];
u3(0,0,-1.1751852380466716) q[9];
cx q[6],q[9];
u3(0,0,-pi/2) q[6];
u1(-pi/2) q[6];
u2(0,pi) q[6];
u1(-pi/2) q[6];
u3(0,0,pi/2) q[6];
cx q[6],q[5];
u3(-1.484544168591007,0,0) q[5];
u3(-1.484544168591007,0,0) q[6];
cx q[6],q[5];
u3(0,0,-pi/2) q[5];
u3(0,0,-6.2751021927880934) q[5];
u(0,0,4.757438636566416) q[5];
u(0,0,2.178889004057346) q[5];
u3(0,0,-pi/2) q[6];
u3(0,0,pi/2) q[6];
u(0,0,-1.8817807149293067) q[6];
cx q[5],q[6];
u(-1.2195962250800982,0,-2.178889004057346) q[6];
cx q[5],q[6];
u(0,0,0.16676624844131993) q[5];
cx q[1],q[5];
u(-1.392281812965215,0,-5.058084904729555) q[5];
cx q[1],q[5];
u(0,0,-2.0007689629965966) q[1];
u(1.392281812965215,4.891318656288235,0) q[5];
u(0,0,3.2662689549442123) q[5];
u(0,0,2.2624475586747814) q[5];
cx q[5],q[1];
u(-1.2416151935769724,0,-2.2624475586747814) q[1];
cx q[5],q[1];
u(1.2416151935769724,4.263216521671378,0) q[1];
u(0,0,1.9523982780716218) q[5];
u(0,0,-pi/2) q[5];
u(1.2195962250800982,4.060669718986652,0) q[6];
u3(0,0,pi/4) q[6];
cx q[6],q[4];
u3(0,0,-pi/4) q[4];
cx q[6],q[4];
u3(0,0,pi/4) q[4];
u3(pi/2,0,pi) q[4];
cx q[2],q[4];
cx q[4],q[2];
u3(0,0,pi/2) q[2];
u(0,0,pi) q[4];
u3(pi/2,0,pi) q[4];
cx q[1],q[4];
u3(0,0,0.5371964698261154) q[4];
cx q[1],q[4];
u(0,0,pi/4) q[1];
u3(pi/2,0,pi) q[4];
cx q[5],q[1];
u(0.5197568114799864,3.3007023279515852,5.628126068424538) q[6];
u(5.329861310906665,-pi/2,pi/2) q[6];
u(0,0,-pi/4) q[9];
u(0,0,3.3863656874980417) q[9];
u3(0,0,-pi/2) q[9];
u3(pi/2,0,pi) q[9];
u3(0,0,-pi/2) q[9];
cx q[9],q[0];
u1(pi/4) q[0];
cx q[8],q[0];
u1(-pi/4) q[0];
u1(pi/4) q[8];
cx q[9],q[0];
u1(pi/4) q[0];
u2(0,pi) q[0];
u3(pi/2,0,pi) q[0];
u3(0,0,pi/2) q[0];
u3(pi/2,0,pi) q[0];
cx q[0],q[2];
cx q[2],q[0];
u3(pi/2,-pi/2,pi/2) q[0];
u3(pi/2,0,pi) q[2];
u3(0,0,1.3883258881074347) q[2];
cx q[6],q[2];
u3(0,0,-1.3883258881074347) q[2];
cx q[6],q[2];
u(0,0,1.6068834021630132) q[2];
cx q[9],q[8];
u1(-pi/4) q[8];
u1(pi/4) q[9];
cx q[9],q[8];
u3(0,0,3.1073146452892844) q[8];
cx q[7],q[8];
u3(0,0,-3.1073146452892844) q[8];
cx q[7],q[8];
u3(pi/2,-pi/2,pi/2) q[8];
cx q[8],q[0];
u3(0,0,2.194776995003038) q[0];
cx q[8],q[0];
u3(-pi/2,-pi/2,pi/2) q[0];
cx q[2],q[0];
u(0,0,-1.6068834021630132) q[0];
cx q[2],q[0];
u(0,0,1.6068834021630132) q[0];
u(0,0,0.8000217984974315) q[2];
u3(-pi/2,-pi/2,pi/2) q[8];
u2(0,pi) q[8];
cx q[4],q[8];
u1(-pi/4) q[8];
cx q[4],q[8];
u(pi,0,pi) q[4];
u2(0,pi) q[8];
u2(0,pi) q[8];
cx q[4],q[8];
u1(-pi/4) q[8];
cx q[4],q[8];
u2(0,pi) q[8];
u(1.7342078375556609,1.862804190683707,3.373771938548499) q[9];
u(0,0,3.086389754996826) q[9];
cx q[9],q[7];
u(0,0,-3.086389754996826) q[7];
cx q[9],q[7];
u(0,0,3.086389754996826) q[7];
u(0,0,0.8085841215538463) q[7];
cx q[7],q[6];
u(0,0,-0.8085841215538463) q[6];
cx q[7],q[6];
u(0,0,0.8085841215538463) q[6];
cx q[3],q[6];
u2(0,pi) q[3];
cx q[6],q[3];
u1(-pi/4) q[3];
u(0,0,-pi/4) q[7];
cx q[7],q[4];
u(0,0,pi/4) q[4];
cx q[7],q[4];
u(0,0,-pi/4) q[4];
cx q[8],q[3];
u1(pi/4) q[3];
cx q[6],q[3];
u1(-pi/4) q[3];
u1(pi/4) q[6];
cx q[8],q[3];
u1(pi/4) q[3];
u2(0,pi) q[3];
cx q[8],q[6];
u1(-pi/4) q[6];
u1(pi/4) q[8];
cx q[8],q[6];
cx q[3],q[6];
u(1.2425144353084185,0.7678660099197766,4.090066112252046) q[9];
u2(0,pi) q[9];
cx q[0],q[9];
u1(-pi/4) q[9];
cx q[0],q[9];
u(pi,0,pi) q[0];
u2(0,pi) q[9];
u2(0,pi) q[9];
cx q[0],q[9];
u1(-pi/4) q[9];
cx q[0],q[9];
u2(0,pi) q[9];
