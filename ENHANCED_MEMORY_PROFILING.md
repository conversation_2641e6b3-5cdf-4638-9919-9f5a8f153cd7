# Enhanced ADAPT-VQE Memory Profiling System

## Overview

The memory profiling system has been completely redesigned to provide clear, detailed, and visually appealing memory consumption reports throughout the ADAPT-VQE process. The new system tracks memory usage at every stage and provides comprehensive breakdowns of where memory is being consumed.

## Key Features

### 1. **Structured Visual Layout**
- Uses Unicode box-drawing characters for professional-looking tables
- Clear section headers with visual separators
- Consistent formatting and alignment
- Color-coded status indicators (✅ ⚠️ 🎯 📊 ⚡)

### 2. **Three-Phase Memory Tracking**

#### **Phase 1: Initial Memory State**
```
╔══════════════════════════════════════════════════════════════════════════════╗
║                    ADAPT-VQE MEMORY PROFILING REPORT                        ║
╚══════════════════════════════════════════════════════════════════════════════╝

┌─ PHASE 1: INITIAL MEMORY STATE ─────────────────────────────────────────────┐
│ CPU Memory (Initial):                                                       │
│   • RSS (Resident Set Size):   256.45 MB                                    │
│   • Peak memory usage:        5675.59 MB                                    │
│   • Virtual memory size:      5643.60 MB                                    │
│                                                                             │
│ GPU Memory (Initial):                                                       │
│   • Total GPU memory:           40.96 GB                                    │
│   • Free GPU memory:            38.84 GB                                    │
│   • Used GPU memory:             2.12 GB                                    │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### **Phase 2: Operator Pool Allocation**
```
┌─ PHASE 2: OPERATOR POOL ALLOCATION ─────────────────────────────────────────┐
│ Pool Configuration:                                                         │
│   • Total operators in pool:      645                                       │
│   • Hamiltonian terms:           7833                                       │
│   • Expected memory scaling: O(N²) where N = pool size                     │
└─────────────────────────────────────────────────────────────────────────────┘

┌─ OPERATOR PROCESSING PROGRESS ──────────────────────────────────────────────┐
│ Op#  │ CPU(MB) │ ΔCpu │ GPU(GB) │ ΔGpu │ Paulis │ Cliques │ Efficiency │
├──────┼─────────┼──────┼─────────┼──────┼────────┼─────────┼────────────┤
│    0 │   256.4 │  0.0 │   14.74 │ 0.00 │  11136 │    3086 │      27.7% │
│      └─ Memory breakdown: Coeffs=86.25KB, ZMasks=86.25KB, Obs=197.50KB     │
│    1 │   258.1 │  1.7 │   14.76 │ 0.02 │  11136 │    2956 │      26.5% │
│   ⚡ MEMORY CLEANUP: Forced cleanup at operator 10 (every 10 ops)           │
└──────┴─────────┴──────┴─────────┴──────┴────────┴─────────┴────────────┘
```

#### **Phase 3: Final Memory Analysis**
```
┌─ PHASE 3: FINAL MEMORY ANALYSIS ────────────────────────────────────────────┐
│ Operator Pool Generation Complete                                           │
│   • Total operators processed:      645                                     │
│   • Total Pauli strings:         416280                                     │
│   • Total commuting groups:      156420                                     │
└─────────────────────────────────────────────────────────────────────────────┘

┌─ MEMORY CONSUMPTION BREAKDOWN ──────────────────────────────────────────────┐
│ CPU Memory Analysis:                                                        │
│   • Initial RSS:                256.45 MB                                   │
│   • Final RSS:                 1245.67 MB                                   │
│   • Net CPU increase:           989.22 MB                                   │
│   • Peak memory usage:         1456.78 MB                                   │
│                                                                             │
│ GPU Memory Analysis:                                                        │
│   • Initial free:                38.84 GB                                   │
│   • Final free:                  27.56 GB                                   │
│   • Net GPU consumption:         11.28 GB                                   │
│   • Total GPU capacity:          40.96 GB                                   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3. **Detailed Component Breakdown**
```
┌─ DETAILED COMPONENT MEMORY BREAKDOWN ───────────────────────────────────────┐
│ Data Structure Analysis:                                                    │
│   • Operators in pool:              645                                     │
│   • Commuting groups:            156420                                     │
│   • Pauli terms total:           416280                                     │
│   • Avg Pauli/operator:             645                                     │
│   • Avg groups/operator:            242                                     │
│                                                                             │
│ Memory Component Breakdown:                                                 │
│   • Coefficients storage:         3.17 MB                                  │
│   • Z-mask storage:               3.17 MB                                  │
│   • Observable structures:       10.02 MB                                  │
│   • Gradient circuits:            0.64 MB                                  │
│   ────────────────────────────────────────────────────────────────────── │
│   • TOTAL ESTIMATED:             17.00 MB                                  │
│                                                                             │
│ Memory Efficiency Analysis:                                                 │
│   • Estimated vs Actual:          85.2%                                    │
│   ✅ GOOD EFFICIENCY: Memory usage within expected range                    │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4. **Enhanced Optimization Phase Tracking**
```
╔══════════════════════════════════════════════════════════════════════════════╗
║                        ADAPT-VQE OPTIMIZATION PHASE                         ║
╚══════════════════════════════════════════════════════════════════════════════╝

┌─ OPTIMIZATION PROGRESS ─────────────────────────────────────────────────────┐
│ Iter │    Energy (Hartree)     │ Evals │ Grad Norm │ Depth │ 1qG │ 2qG │ GPU(GB) │
├──────┼─────────────────────────┼───────┼───────────┼───────┼─────┼─────┼─────────┤
│    0 │ -76.184892889838150000  │    19 │  7.39e-01 │    84 │ 114 │  48 │   14.74 │
│      └─ Selected: (16^ 5^ 15 4 )                                            │
├──────┴─────────────────────────┴───────┴───────────┴───────┴─────┴─────┴─────────┤
│ 📊 MEMORY SNAPSHOT (Iteration  0)                                          │
│   • CPU RSS:         1245.6 MB                                              │
│   • GPU Used:          14.74 GB                                              │
│   • GPU Free:          27.56 GB                                              │
│   • Circuit Depth:        84                                                  │
│   • Parameters:            1                                                  │
├──────┬─────────────────────────┬───────┬───────────┬───────┬─────┬─────┬─────────┤
│    1 │ -76.201234567890120000  │    25 │  5.12e-01 │   168 │ 228 │  96 │   14.76 │
│      └─ Selected: (12^ 8^ 11 7 )                                            │
└──────┴─────────────────────────┴───────┴───────────┴───────┴─────┴─────┴─────────┘

🎯 ADAPT-VQE Optimization completed after 15 iterations
```

## Key Improvements

### 1. **Real-time Memory Tracking**
- Progress updates every 25 operators (instead of 50)
- Memory deltas showing incremental changes
- Immediate cleanup notifications

### 2. **Memory Efficiency Analysis**
- Compares estimated vs actual memory usage
- Provides efficiency ratings with visual indicators
- Warns about potential memory leaks

### 3. **Component-wise Breakdown**
- Separate tracking for coefficients, Z-masks, observables
- Gradient circuit memory estimation
- Per-operator memory analysis for first few operators

### 4. **Enhanced Optimization Tracking**
- Compact table format with all essential information
- Memory snapshots every 3 iterations
- Selected operator details
- Circuit complexity metrics

## Benefits

1. **Better Debugging**: Easy identification of memory hotspots
2. **Performance Monitoring**: Real-time efficiency analysis
3. **Resource Planning**: Accurate memory usage predictions
4. **Visual Clarity**: Professional, easy-to-read output format
5. **Comprehensive Coverage**: Every phase of ADAPT-VQE is monitored

## Usage

The enhanced memory profiling is automatically enabled when running ADAPT-VQE. No additional configuration is required. The system will:

1. Display initial memory state before operator pool construction
2. Show progress during operator processing with memory breakdowns
3. Provide comprehensive final analysis
4. Track memory usage during optimization iterations

This provides complete visibility into memory consumption patterns and helps identify potential optimization opportunities or memory issues.
